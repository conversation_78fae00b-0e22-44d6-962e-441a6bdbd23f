package com.ecm.webservice.entity.pojo;

import java.util.Date;

/**
 * @Description :
 * <功能详细描述>
 * @author: 须尽欢_____
 * @Data : 2022/10/21 9:42
 */
public class EcmSysObject extends EcmObject {

    private static final long serialVersionUID = 1L;
    protected String name;
    protected String description;
    protected String creator;
    protected Date creationDate = new Date();
    protected String modifier;
    protected Date modifiedDate = new Date();

    public EcmSysObject() {
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getCreationDate() {
        return this.creationDate;
    }

    public void setCreationDate(Date creationDate) {
        if (this.modifiedDate == null) {
            this.modifiedDate = creationDate;
        }

        this.creationDate = creationDate;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        if (this.modifier == null) {
            this.modifier = creator;
        }

        this.creator = creator;
        if (this.attributes != null) {
            this.attributes.put("CREATOR", creator);
        }

    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
        if (this.attributes != null) {
            this.attributes.put("MODIFIER", modifier);
        }

    }

    public Date getModifiedDate() {
        return this.modifiedDate;
    }

    public void setModifiedDate(Date dt) {
        this.modifiedDate = dt;
        if (this.attributes != null) {
            this.attributes.put("MODIFIED_DATE", dt);
        }

    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    protected String getString(Object val) {
        return val == null ? null : val.toString();
    }


}
