package com.ecm.webservice.config;

import org.apache.cxf.interceptor.Fault;
import org.apache.cxf.message.Message;
import org.apache.cxf.phase.AbstractPhaseInterceptor;
import org.apache.cxf.phase.Phase;
import org.apache.cxf.service.model.ServiceInfo;

/**
 * @Description : 忽略到targetnamespace的校验
 * <功能详细描述>
 * @author: 须尽欢_____
 * @Data : 2022/11/14 16:30
 */
public class CxfIgnoreTargetNamespaceInInterceptors extends AbstractPhaseInterceptor<Message> {

    public CxfIgnoreTargetNamespaceInInterceptors() {
        super(Phase.RECEIVE);
    }

    @Override
    public void handleMessage(Message message) throws Fault {
        for (ServiceInfo si : message.getExchange().getService().getServiceInfos()) {
            //这个就是忽略客户端不带命名空间的关键，可查看类DocLiteralInInterceptor的handleMessage方法
            si.setProperty("soap.force.doclit.bare", Boolean.TRUE);
        }
    }

}
