package com.ecm.webservice.entity.pojo;

import java.util.Date;

/**
 * @Description :
 * <功能详细描述>
 * @author: 须尽欢_____
 * @Data : 2022/10/21 9:52
 */
public class EcmUser extends EcmSysObject {

    private static final long serialVersionUID = 1L;
    private String loginName;
    private String phone;
    private String email;
    private int isActived;
    private String groupName;
    private String password;
    private String passwordConfirm;
    private String groupId;
    private Integer loginType;
    private String ldapCn;
    private Integer clientPermission;
    private Integer systemPermission;
    private String ldapName;
    private String extendId;
    private String extendGroupId;
    private String signImage;
    private String delegateUser;
    private Date delegateStart;
    private Date delegateEnd;
    private String CompanyName;
    private String departmentName;
    private String jobTitle;
    private String myUsers;
    private String nameChinese;
    private String nameChineseShort;
    private String sysId;
    private String extendInfo;
    private String userType;

    public EcmUser() {
    }

    public String getSysId() {
        return this.sysId;
    }

    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

    public String getExtendInfo() {
        return this.extendInfo;
    }

    public void setExtendInfo(String extendInfo) {
        this.extendInfo = extendInfo;
    }

    public String getUserType() {
        return this.userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getSignImage() {
        return this.signImage;
    }

    public void setSignImage(String signImage) {
        this.signImage = signImage;
    }

    public String getLoginName() {
        return this.loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName == null ? null : loginName.trim();
    }

    public String getPhone() {
        return this.phone;
    }

    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    public int getIsActived() {
        return this.isActived;
    }

    public void setIsActived(int isActived) {
        this.isActived = isActived;
    }

    public String getGroupName() {
        return this.groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName == null ? null : groupName.trim();
    }

    public String getPassword() {
        return this.password;
    }

    public void setPassword(String password) {
        this.password = password == null ? null : password.trim();
        if (this.passwordConfirm == null) {
            this.passwordConfirm = this.password;
        }

    }

    public String getGroupId() {
        return this.groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public Integer getLoginType() {
        return this.loginType;
    }

    public void setLoginType(Integer loginType) {
        this.loginType = loginType;
    }

    public String getLdapCn() {
        return this.ldapCn;
    }

    public void setLdapCn(String ldapCn) {
        this.ldapCn = ldapCn == null ? null : ldapCn.trim();
    }

    public Integer getClientPermission() {
        return this.clientPermission;
    }

    public void setClientPermission(Integer clientPermission) {
        this.clientPermission = clientPermission;
    }

    public Integer getSystemPermission() {
        return this.systemPermission;
    }

    public void setSystemPermission(Integer systemPermission) {
        this.systemPermission = systemPermission;
    }

    public String getLdapName() {
        return this.ldapName;
    }

    public void setLdapName(String ldapName) {
        this.ldapName = ldapName == null ? null : ldapName.trim();
    }

    public String getExtendId() {
        return this.extendId;
    }

    public void setExtendId(String extendId) {
        this.extendId = extendId == null ? null : extendId.trim();
    }

    public String getExtendGroupId() {
        return this.extendGroupId;
    }

    public void setExtendGroupId(String extendGroupId) {
        this.extendGroupId = extendGroupId == null ? null : extendGroupId.trim();
    }

    public Date getDelegateStart() {
        return this.delegateStart;
    }

    public void setDelegateStart(Date delegateStart) {
        this.delegateStart = delegateStart;
    }

    public String getDelegateUser() {
        return this.delegateUser;
    }

    public void setDelegateUser(String delegateUser) {
        this.delegateUser = delegateUser;
    }

    public Date getDelegateEnd() {
        return this.delegateEnd;
    }

    public void setDelegateEnd(Date delegateEnd) {
        this.delegateEnd = delegateEnd;
    }

    public String getPasswordConfirm() {
        return this.passwordConfirm;
    }

    public void setPasswordConfirm(String passwordConfirm) {
        this.passwordConfirm = passwordConfirm;
    }

    public String getCompanyName() {
        return this.CompanyName;
    }

    public void setCompanyName(String companyName) {
        this.CompanyName = companyName;
    }

    public String getJobTitle() {
        return this.jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public String getDepartmentName() {
        return this.departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getMyUsers() {
        return this.myUsers;
    }

    public void setMyUsers(String myUsers) {
        this.myUsers = myUsers;
    }

    public String getNameChinese() {
        return this.nameChinese;
    }

    public void setNameChinese(String nameChinese) {
        this.nameChinese = nameChinese;
    }

    public String getNameChineseShort() {
        return this.nameChineseShort;
    }

    public void setNameChineseShort(String nameChineseShort) {
        this.nameChineseShort = nameChineseShort;
    }

}
