package com.ecm.webservice.entity.pojo;

import java.util.List;

/**
 * @Description :
 * <功能详细描述>
 * @author: 须尽欢_____
 * @Data : 2022/10/21 9:45
 */
public class EcmGroup extends EcmSysObject {

    private static final long serialVersionUID = 1L;
    private String coding;
    private String groupType;
    private String displayName;
    private String parentId;
    private boolean extended = false;
    private String extendId;
    private String sysId;
    private String extendInfo;
    private String manager;
    List<EcmGroup> children;

    public EcmGroup() {
    }

    public String getDisplayName() {
        return this.displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getSysId() {
        return this.sysId;
    }

    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

    public String getExtendInfo() {
        return this.extendInfo;
    }

    public void setExtendInfo(String extendInfo) {
        this.extendInfo = extendInfo;
    }

    public List<EcmGroup> getChildren() {
        return this.children;
    }

    public void setChildren(List<EcmGroup> children) {
        this.children = children;
    }

    public String getCoding() {
        return this.coding;
    }

    public void setCoding(String coding) {
        this.coding = coding == null ? null : coding.trim();
    }

    public String getGroupType() {
        return this.groupType;
    }

    public void setGroupType(String groupType) {
        this.groupType = groupType == null ? null : groupType.trim();
    }

    public String getParentId() {
        return this.parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public boolean isExtended() {
        return this.extended;
    }

    public void setExtended(boolean extended) {
        this.extended = extended;
    }

    public String getExtendId() {
        return this.extendId;
    }

    public void setExtendId(String extendId) {
        this.extendId = extendId;
    }

    public String getManager() {
        return this.manager;
    }

    public void setManager(String manager) {
        this.manager = manager;
    }

}
