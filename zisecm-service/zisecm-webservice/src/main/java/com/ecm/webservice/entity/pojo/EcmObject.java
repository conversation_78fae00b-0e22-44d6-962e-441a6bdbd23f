package com.ecm.webservice.entity.pojo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description :
 * <功能详细描述>
 * @author: 须尽欢_____
 * @Data : 2022/10/21 9:33
 */
public class EcmObject implements Serializable {

    private static final long serialVersionUID = 1L;
    protected String id;
    protected String typeName;
    protected Map<String, Object> attributes = new HashMap();
    protected String fields;

    public EcmObject() {
    }

    public void setAttributes(Map<String, Object> attributes) {
        this.attributes = attributes;
        if (attributes != null) {
            if (attributes.get("ID") != null) {
                this.setId((String)attributes.get("ID"));
            }

            if (attributes.get("TYPE_NAME") != null) {
                this.setTypeName((String)attributes.get("TYPE_NAME"));
            }
        }

    }

    public void addAttribute(String key, Object value) {
        if (this.attributes == null) {
            this.attributes = new HashMap();
        }

        if ("ID".equals(key.toUpperCase())) {
            this.setId((String)this.attributes.get("ID"));
        } else if ("TYPE_NAME".equals(key.toUpperCase())) {
            this.setTypeName((String)this.attributes.get("TYPE_NAME"));
        }

        this.attributes.put(key.toUpperCase(), value);
    }

    public Map<String, Object> getAttributes() {
        return this.attributes;
    }

    public String getTypeName() {
        return this.typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName == null ? null : typeName.trim();
        if (this.attributes != null) {
            this.attributes.put("TYPE_NAME", this.typeName);
        }

    }

    public String getFields() {
        return this.fields;
    }

    public void setFields(String fields) {
        this.fields = fields;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
        if (this.attributes != null) {
            this.attributes.put("ID", id == null ? "" : id.trim());
        }

    }


}
