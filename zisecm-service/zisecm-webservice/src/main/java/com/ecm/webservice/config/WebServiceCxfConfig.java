package com.ecm.webservice.config;

import com.ecm.webservice.service.dept.DeptSyncServiceImpl;
import com.ecm.webservice.service.tempuser.TempUserSyncServiceImpl;
import com.ecm.webservice.service.user.UserSyncServiceImpl;
import org.apache.cxf.Bus;
import org.apache.cxf.bus.spring.SpringBus;
import org.apache.cxf.interceptor.Interceptor;
import org.apache.cxf.jaxws.EndpointImpl;
import org.apache.cxf.message.Message;
import org.apache.cxf.transport.servlet.CXFServlet;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.xml.ws.Endpoint;
import java.util.List;

/**
 * @Description :
 * <功能详细描述>
 * @author: 须尽欢_____
 * @Data : 2022/10/12 18:20
 */
@Configuration
public class WebServiceCxfConfig {

    @Bean
    public ServletRegistrationBean disServlet() {
        ServletRegistrationBean servletRegistrationBean = new ServletRegistrationBean(new CXFServlet(), "/webService/*");
        return servletRegistrationBean;
    }
    @Bean(name = Bus.DEFAULT_BUS_ID)
    public SpringBus springBus() {
        return new SpringBus();
    }

    @Bean
    public Endpoint syncDeptPoint() {
        EndpointImpl endpoint = new EndpointImpl(springBus(), new DeptSyncServiceImpl());
        // endpoint.getInInterceptors().add(new WebServiceAuthInterceptor());
        // 设置忽略targetnamespace的拦截器
        setIgnoreTargetNamespaceInInterceptors(endpoint);
        endpoint.publish("/DeptSyncService");
        return endpoint;
    }

    @Bean
    public Endpoint syncUserPoint() {
        EndpointImpl endpoint = new EndpointImpl(springBus(), new UserSyncServiceImpl());
        // endpoint.getInInterceptors().add(new WebServiceAuthInterceptor());
        // 设置忽略targetnamespace的拦截器
        setIgnoreTargetNamespaceInInterceptors(endpoint);
        endpoint.publish("/UserSyncService");
        return endpoint;
    }

    @Bean
    public Endpoint syncTmpUserPoint() {
        EndpointImpl endpoint = new EndpointImpl(springBus(), new TempUserSyncServiceImpl());
        // endpoint.getInInterceptors().add(new WebServiceAuthInterceptor());
        // 设置忽略targetnamespace的拦截器
        setIgnoreTargetNamespaceInInterceptors(endpoint);
        endpoint.publish("/TempUserSyncService");
        return endpoint;
    }

    /**
     * cxf webservice服务端设置服务端忽略target namespace的校验
     *
     * @param endpoint 服务端
     */
    public void setIgnoreTargetNamespaceInInterceptors(EndpointImpl endpoint) {
        List<Interceptor<? extends Message>> inInterceptors = endpoint.getInInterceptors();
        inInterceptors.add(new CxfIgnoreTargetNamespaceInInterceptors());
        endpoint.setInInterceptors(inInterceptors);
    }

}
