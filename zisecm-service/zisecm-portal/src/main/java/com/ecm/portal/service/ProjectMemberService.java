package com.ecm.portal.service;

import com.ecm.core.entity.Pager;
import com.ecm.portal.entity.ProjectMemberEntity;

import java.util.List;
import java.util.Map;

/**
 * 项目成员管理服务接口
 * 兼容MyBatis 2.1.0 + MyBatis-Plus 3.4.2
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ProjectMemberService {
    
    /**
     * 添加项目成员
     * @param member 成员实体
     * @return 成员ID
     */
    String addProjectMember(ProjectMemberEntity member);
    
    /**
     * 更新项目成员
     * @param member 成员实体
     * @return 是否成功
     */
    boolean updateProjectMember(ProjectMemberEntity member);
    
    /**
     * 移除项目成员（软删除）
     * @param memberId 成员ID
     * @return 是否成功
     */
    boolean removeProjectMember(String memberId);
    
    /**
     * 批量添加项目成员
     * @param projectId 项目ID
     * @param userIds 用户ID列表
     * @param role 角色
     * @param permissionLevel 权限级别
     * @return 成功添加的数量
     */
    int addProjectMembers(String projectId, List<String> userIds, Integer role, Integer permissionLevel);
    
    /**
     * 批量移除项目成员
     * @param memberIds 成员ID列表
     * @return 成功移除的数量
     */
    int removeProjectMembers(List<String> memberIds);
    
    /**
     * 获取项目成员列表
     * @param projectId 项目ID
     * @param pager 分页参数
     * @return 成员列表
     */
    List<Map<String, Object>> getProjectMembers(String projectId, Pager pager);
    
    /**
     * 获取项目成员总数
     * @param projectId 项目ID
     * @return 总数
     */
    int getProjectMemberCount(String projectId);
    
    /**
     * 获取项目成员详情
     * @param memberId 成员ID
     * @return 成员详情
     */
    Map<String, Object> getProjectMember(String memberId);
    
    /**
     * 根据用户获取项目成员
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 成员信息
     */
    Map<String, Object> getProjectMemberByUser(String projectId, String userId);
    
    /**
     * 获取用户参与的项目成员关系
     * @param userId 用户ID
     * @param pager 分页参数
     * @return 成员关系列表
     */
    List<Map<String, Object>> getUserProjectMemberships(String userId, Pager pager);
    
    /**
     * 搜索项目成员
     * @param projectId 项目ID
     * @param keyword 关键词
     * @param pager 分页参数
     * @return 成员列表
     */
    List<Map<String, Object>> searchProjectMembers(String projectId, String keyword, Pager pager);
    
    /**
     * 更新成员角色
     * @param memberId 成员ID
     * @param role 新角色
     * @param userId 操作用户ID
     * @return 是否成功
     */
    boolean updateMemberRole(String memberId, Integer role, String userId);
    
    /**
     * 更新成员权限
     * @param memberId 成员ID
     * @param permissionLevel 权限级别
     * @param userId 操作用户ID
     * @return 是否成功
     */
    boolean updateMemberPermission(String memberId, Integer permissionLevel, String userId);
    
    /**
     * 更新成员状态
     * @param memberId 成员ID
     * @param status 状态
     * @param userId 操作用户ID
     * @return 是否成功
     */
    boolean updateMemberStatus(String memberId, Integer status, String userId);
    
    /**
     * 检查是否为项目成员
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否为成员
     */
    boolean isProjectMember(String projectId, String userId);
    
    /**
     * 检查是否为项目管理员
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否为管理员
     */
    boolean isProjectManager(String projectId, String userId);
    
    /**
     * 检查成员权限
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param permission 权限名称
     * @return 是否有权限
     */
    boolean hasMemberPermission(String projectId, String userId, String permission);
    
    /**
     * 转移项目管理员
     * @param projectId 项目ID
     * @param fromUserId 原管理员用户ID
     * @param toUserId 新管理员用户ID
     * @param operatorId 操作者ID
     * @return 是否成功
     */
    boolean transferProjectManager(String projectId, String fromUserId, String toUserId, String operatorId);
    
    /**
     * 邀请用户加入项目
     * @param projectId 项目ID
     * @param userIds 用户ID列表
     * @param role 角色
     * @param inviterId 邀请者ID
     * @return 邀请结果
     */
    Map<String, Object> inviteUsersToProject(String projectId, List<String> userIds, Integer role, String inviterId);
    
    /**
     * 接受项目邀请
     * @param memberId 成员ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean acceptProjectInvitation(String memberId, String userId);
    
    /**
     * 拒绝项目邀请
     * @param memberId 成员ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean rejectProjectInvitation(String memberId, String userId);
    
    /**
     * 获取成员权限列表
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 权限列表
     */
    List<String> getMemberPermissions(String projectId, String userId);
    
    /**
     * 获取项目成员统计
     * @param projectId 项目ID
     * @return 统计信息
     */
    Map<String, Object> getProjectMemberStatistics(String projectId);
    
    /**
     * 按角色获取项目成员
     * @param projectId 项目ID
     * @return 按角色分组的成员列表
     */
    Map<String, List<Map<String, Object>>> getProjectMembersByRole(String projectId);
}
