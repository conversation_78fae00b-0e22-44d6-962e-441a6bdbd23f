package com.ecm.portal.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * 项目阶段实体类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ProjectPhaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 阶段ID
     */
    private String id;
    
    /**
     * 项目ID
     */
    private String projectId;
    
    /**
     * 阶段名称
     */
    private String phaseName;
    
    /**
     * 阶段编码
     */
    private String phaseCode;
    
    /**
     * 阶段描述
     */
    private String description;
    
    /**
     * 阶段状态：0-未开始，1-进行中，2-已完成，3-已暂停，4-已取消
     */
    private Integer status;
    
    /**
     * 计划开始日期
     */
    private Date plannedStartDate;
    
    /**
     * 计划结束日期
     */
    private Date plannedEndDate;
    
    /**
     * 实际开始日期
     */
    private Date startDate;
    
    /**
     * 实际结束日期
     */
    private Date endDate;
    
    /**
     * 阶段进度百分比
     */
    private Integer progress;
    
    /**
     * 排序索引
     */
    private Integer orderIndex;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 创建时间
     */
    private Date creationDate;
    
    /**
     * 修改人
     */
    private String modifier;
    
    /**
     * 修改时间
     */
    private Date modifiedDate;
    
    /**
     * 是否删除：0-否，1-是
     */
    private Integer deleted;
    
    // 构造函数
    public ProjectPhaseEntity() {}
    
    public ProjectPhaseEntity(String projectId, String phaseName, String phaseCode) {
        this.projectId = projectId;
        this.phaseName = phaseName;
        this.phaseCode = phaseCode;
        this.status = 0; // 默认未开始
        this.progress = 0; // 默认进度0%
        this.deleted = 0; // 默认未删除
        this.creationDate = new Date();
        this.modifiedDate = new Date();
    }
    
    // Getter和Setter方法
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getProjectId() {
        return projectId;
    }
    
    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }
    
    public String getPhaseName() {
        return phaseName;
    }
    
    public void setPhaseName(String phaseName) {
        this.phaseName = phaseName;
    }
    
    public String getPhaseCode() {
        return phaseCode;
    }
    
    public void setPhaseCode(String phaseCode) {
        this.phaseCode = phaseCode;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Date getPlannedStartDate() {
        return plannedStartDate;
    }
    
    public void setPlannedStartDate(Date plannedStartDate) {
        this.plannedStartDate = plannedStartDate;
    }
    
    public Date getPlannedEndDate() {
        return plannedEndDate;
    }
    
    public void setPlannedEndDate(Date plannedEndDate) {
        this.plannedEndDate = plannedEndDate;
    }
    
    public Date getStartDate() {
        return startDate;
    }
    
    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }
    
    public Date getEndDate() {
        return endDate;
    }
    
    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }
    
    public Integer getProgress() {
        return progress;
    }
    
    public void setProgress(Integer progress) {
        this.progress = progress;
    }
    
    public Integer getOrderIndex() {
        return orderIndex;
    }
    
    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }
    
    public String getCreator() {
        return creator;
    }
    
    public void setCreator(String creator) {
        this.creator = creator;
    }
    
    public Date getCreationDate() {
        return creationDate;
    }
    
    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }
    
    public String getModifier() {
        return modifier;
    }
    
    public void setModifier(String modifier) {
        this.modifier = modifier;
    }
    
    public Date getModifiedDate() {
        return modifiedDate;
    }
    
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }
    
    public Integer getDeleted() {
        return deleted;
    }
    
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }
    
    @Override
    public String toString() {
        return "ProjectPhaseEntity{" +
                "id='" + id + '\'' +
                ", projectId='" + projectId + '\'' +
                ", phaseName='" + phaseName + '\'' +
                ", phaseCode='" + phaseCode + '\'' +
                ", description='" + description + '\'' +
                ", status=" + status +
                ", plannedStartDate=" + plannedStartDate +
                ", plannedEndDate=" + plannedEndDate +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", progress=" + progress +
                ", orderIndex=" + orderIndex +
                ", creator='" + creator + '\'' +
                ", creationDate=" + creationDate +
                ", modifier='" + modifier + '\'' +
                ", modifiedDate=" + modifiedDate +
                ", deleted=" + deleted +
                '}';
    }
} 