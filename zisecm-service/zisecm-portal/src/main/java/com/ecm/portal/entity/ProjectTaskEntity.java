package com.ecm.portal.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * 项目任务实体类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ProjectTaskEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 任务ID
     */
    private String id;
    
    /**
     * 项目ID
     */
    private String projectId;
    
    /**
     * 阶段ID
     */
    private String phaseId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 任务编码
     */
    private String taskCode;
    
    /**
     * 任务描述
     */
    private String description;
    
    /**
     * 任务状态：0-未开始，1-进行中，2-已完成，3-已暂停，4-已取消
     */
    private Integer status;
    
    /**
     * 任务优先级：1-低，2-中，3-高，4-紧急
     */
    private Integer priority;
    
    /**
     * 任务负责人
     */
    private String assignedTo;
    
    /**
     * 任务负责人姓名
     */
    private String assignedName;
    
    /**
     * 计划开始日期
     */
    private Date plannedStartDate;
    
    /**
     * 计划结束日期
     */
    private Date plannedEndDate;
    
    /**
     * 实际开始日期
     */
    private Date startDate;
    
    /**
     * 实际结束日期
     */
    private Date endDate;
    
    /**
     * 任务进度百分比
     */
    private Integer progress;
    
    /**
     * 预估工时（小时）
     */
    private Integer estimatedHours;
    
    /**
     * 实际工时（小时）
     */
    private Integer actualHours;
    
    /**
     * 排序索引
     */
    private Integer orderIndex;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 创建时间
     */
    private Date creationDate;
    
    /**
     * 修改人
     */
    private String modifier;
    
    /**
     * 修改时间
     */
    private Date modifiedDate;
    
    /**
     * 是否删除：0-否，1-是
     */
    private Integer deleted;
    
    // 构造函数
    public ProjectTaskEntity() {}
    
    public ProjectTaskEntity(String projectId, String phaseId, String taskName, String taskCode) {
        this.projectId = projectId;
        this.phaseId = phaseId;
        this.taskName = taskName;
        this.taskCode = taskCode;
        this.status = 0; // 默认未开始
        this.priority = 2; // 默认中等优先级
        this.progress = 0; // 默认进度0%
        this.deleted = 0; // 默认未删除
        this.creationDate = new Date();
        this.modifiedDate = new Date();
    }
    
    // Getter和Setter方法
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getProjectId() {
        return projectId;
    }
    
    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }
    
    public String getPhaseId() {
        return phaseId;
    }
    
    public void setPhaseId(String phaseId) {
        this.phaseId = phaseId;
    }
    
    public String getTaskName() {
        return taskName;
    }
    
    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }
    
    public String getTaskCode() {
        return taskCode;
    }
    
    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Integer getPriority() {
        return priority;
    }
    
    public void setPriority(Integer priority) {
        this.priority = priority;
    }
    
    public String getAssignedTo() {
        return assignedTo;
    }
    
    public void setAssignedTo(String assignedTo) {
        this.assignedTo = assignedTo;
    }
    
    public String getAssignedName() {
        return assignedName;
    }
    
    public void setAssignedName(String assignedName) {
        this.assignedName = assignedName;
    }
    
    public Date getPlannedStartDate() {
        return plannedStartDate;
    }
    
    public void setPlannedStartDate(Date plannedStartDate) {
        this.plannedStartDate = plannedStartDate;
    }
    
    public Date getPlannedEndDate() {
        return plannedEndDate;
    }
    
    public void setPlannedEndDate(Date plannedEndDate) {
        this.plannedEndDate = plannedEndDate;
    }
    
    public Date getStartDate() {
        return startDate;
    }
    
    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }
    
    public Date getEndDate() {
        return endDate;
    }
    
    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }
    
    public Integer getProgress() {
        return progress;
    }
    
    public void setProgress(Integer progress) {
        this.progress = progress;
    }
    
    public Integer getEstimatedHours() {
        return estimatedHours;
    }
    
    public void setEstimatedHours(Integer estimatedHours) {
        this.estimatedHours = estimatedHours;
    }
    
    public Integer getActualHours() {
        return actualHours;
    }
    
    public void setActualHours(Integer actualHours) {
        this.actualHours = actualHours;
    }
    
    public Integer getOrderIndex() {
        return orderIndex;
    }
    
    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }
    
    public String getCreator() {
        return creator;
    }
    
    public void setCreator(String creator) {
        this.creator = creator;
    }
    
    public Date getCreationDate() {
        return creationDate;
    }
    
    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }
    
    public String getModifier() {
        return modifier;
    }
    
    public void setModifier(String modifier) {
        this.modifier = modifier;
    }
    
    public Date getModifiedDate() {
        return modifiedDate;
    }
    
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }
    
    public Integer getDeleted() {
        return deleted;
    }
    
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }
    
    @Override
    public String toString() {
        return "ProjectTaskEntity{" +
                "id='" + id + '\'' +
                ", projectId='" + projectId + '\'' +
                ", phaseId='" + phaseId + '\'' +
                ", taskName='" + taskName + '\'' +
                ", taskCode='" + taskCode + '\'' +
                ", description='" + description + '\'' +
                ", status=" + status +
                ", priority=" + priority +
                ", assignedTo='" + assignedTo + '\'' +
                ", assignedName='" + assignedName + '\'' +
                ", plannedStartDate=" + plannedStartDate +
                ", plannedEndDate=" + plannedEndDate +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", progress=" + progress +
                ", estimatedHours=" + estimatedHours +
                ", actualHours=" + actualHours +
                ", orderIndex=" + orderIndex +
                ", creator='" + creator + '\'' +
                ", creationDate=" + creationDate +
                ", modifier='" + modifier + '\'' +
                ", modifiedDate=" + modifiedDate +
                ", deleted=" + deleted +
                '}';
    }
} 