package com.ecm.portal.controller.project;

import com.ecm.core.PermissionContext;
import com.ecm.core.entity.EcmAcl;
import com.ecm.core.entity.EcmDocument;
import com.ecm.core.exception.AccessDeniedException;
import com.ecm.core.exception.EcmException;
import com.ecm.core.exception.NoPermissionException;
import com.ecm.core.service.AclService;
import com.ecm.core.service.DocumentService;
import com.ecm.core.util.AjaxResult;
import com.ecm.portal.controller.ControllerAbstract;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ecm.portal.log.MethodLogAnnotation;

import java.util.List;
import java.util.Map;


@RestController
@MethodLogAnnotation
@RequestMapping("/projectMgt")
public class ProjectManage extends ControllerAbstract {
    @Autowired
    DocumentService documentService;
    @Autowired
    AclService aclService;

    /**
     * 验证所选择删除的项目中是否包含文件
     * @param projectCodes
     * @return
     */
    @PostMapping("/projectHasFile")
    public AjaxResult projectHasFile(@RequestParam(name = "projectCodes",required = true) String[] projectCodes){

        String projectStr= String.join("','",projectCodes);
        String sql="select * from ECM_DOCUMENT where C_CODE4 in('"+projectStr+"') and TYPE_NAME like '项目文件%'";
        List<Map<String,Object>> result= null;
        try {
            result = documentService.getMapList(getToken(),sql);
        } catch (EcmException e) {
            e.printStackTrace();
        } catch (AccessDeniedException e) {
            e.printStackTrace();
        }
        if(result!=null&&result.size()>0){
            return AjaxResult.error("所选择的项目中有文件，不允许删除！");
        }
        return AjaxResult.success("ok");
    }

    /**
     * 根据项目生成项目ACL，ecm_proj_项目ID
     * @param projectId
     * @return
     */
    @PostMapping("/grandProjectUser")
    public AjaxResult grantProjectUser(@RequestParam(name = "projectId",required = true) String projectId){

        try {
            EcmDocument project=documentService.getObjectById(getToken(),projectId);
            Object projectManager=project.getAttributeValue("C_REVIEWER2");
            Object projectMember=project.getAttributeValue("C_REVIEWER1");
            String aclName="ecm_proj_"+projectId;

            String projectAclName= project.getAclName();
            if(StringUtils.isEmpty(projectAclName)){
                project.setAclName(aclName);
            }else if(!projectAclName.equals(aclName)){
                project.setAclName(aclName);
            }
            documentService.updateObject(getToken(),project);
            EcmAcl aclProj= aclService.getObjectByName(getToken(),aclName);
            if(aclProj==null){
                aclProj=new EcmAcl();
                aclProj.setName(aclName);
                aclService.newObjectNoEveryOne(getToken(),aclProj);
            }

            /**********项目成员授权****************/
            if(projectMember!=null){
                String[] targetNames = projectMember.toString().split(";");
                for (String targetName:targetNames) {
                    aclService.grantUser(getToken(), aclProj.getId(), targetName, PermissionContext.ObjectPermission.READ, null);
                }
            }
            /*********项目管理员授权***********/
            if(projectManager!=null){
                String[] targetNames = projectManager.toString().split(";");
                for (String targetName:targetNames) {
                    aclService.grantUser(getToken(), aclProj.getId(), targetName, PermissionContext.ObjectPermission.PEMISSION, null);
                }

            }
//            project.setAclName(aclProj.getName());
//            documentService.updateObject(getToken(),project);
        } catch (EcmException e) {
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        } catch (AccessDeniedException e) {
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        } catch (NoPermissionException e) {
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success("授权成功！");
    }

    /**
     * 将项目ACL赋值给项目文件
     * @param docId
     * @return
     */
    @PostMapping("/assignmentAcl")
    public AjaxResult assignmentAcl(@RequestParam(name = "docId",required = true) String docId,
                                    @RequestParam(name="projectId",required = true) String projectId){
        try {
            EcmDocument doc= documentService.getObjectById(getToken(),docId);
            String aclName="ecm_proj_"+projectId;
            String sql=" select * from ecm_acl where name='"+aclName+"' ";
            List<Map<String,Object>> aclList= documentService.getMapList(getToken(),sql);
            if(aclList==null||aclList.size()==0){
                return AjaxResult.error("请联系管理员检查此acl（ecm_proj_"+projectId+"）是否存在！");
            }

            doc.setAclName(aclName);
            documentService.updateObject(getToken(),doc);
            return AjaxResult.success("授权成功！");
        } catch (EcmException e) {
            e.printStackTrace();
        } catch (AccessDeniedException e) {
            e.printStackTrace();
        } catch (NoPermissionException e) {
            e.printStackTrace();
        }

        return AjaxResult.error("授权失败");
    }

}
