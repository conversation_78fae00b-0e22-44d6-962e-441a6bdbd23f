package com.ecm.portal.service.impl;

import com.ecm.core.entity.Pager;
import com.ecm.portal.entity.ProjectTaskEntity;
import com.ecm.portal.mapper.ProjectTaskMapper;
import com.ecm.portal.service.ProjectTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 项目任务管理服务实现类
 * 兼容MyBatis 2.1.0 + MyBatis-Plus 3.4.2
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class ProjectTaskServiceImpl implements ProjectTaskService {
    
    private static final Logger logger = LoggerFactory.getLogger(ProjectTaskServiceImpl.class);
    
    @Autowired
    private ProjectTaskMapper projectTaskMapper;
    
    @Override
    public String createTask(ProjectTaskEntity task) {
        try {
            int result = projectTaskMapper.insertTask(task);
            return result > 0 ? task.getId() : null;
        } catch (Exception e) {
            logger.error("创建任务失败", e);
            return null;
        }
    }
    
    @Override
    public boolean updateTask(ProjectTaskEntity task) {
        try {
            int result = projectTaskMapper.updateTask(task);
            return result > 0;
        } catch (Exception e) {
            logger.error("更新任务失败", e);
            return false;
        }
    }
    
    @Override
    public boolean deleteTask(String taskId) {
        try {
            int result = projectTaskMapper.deleteTask(taskId);
            return result > 0;
        } catch (Exception e) {
            logger.error("删除任务失败", e);
            return false;
        }
    }
    
    @Override
    public Map<String, Object> getTaskById(String taskId) {
        try {
            Map<String, Object> task = projectTaskMapper.selectTaskById(taskId);
            if (task != null) {
                return convertTaskFields(task);
            }
            return null;
        } catch (Exception e) {
            logger.error("获取任务详情失败", e);
            return null;
        }
    }
    
    @Override
    public List<Map<String, Object>> getProjectTasks(Map<String, Object> conditions, Pager pager) {
        try {
            List<Map<String, Object>> allTasks = projectTaskMapper.selectProjectTasks(conditions, null);
            logger.info("从数据库查询到{}条任务记录", allTasks.size());

            // 转换字段名为前端期望的格式
            List<Map<String, Object>> convertedTasks = new ArrayList<>();
            for (Map<String, Object> task : allTasks) {
                logger.debug("转换任务数据: ID={}, TASK_NAME={}", task.get("ID"), task.get("TASK_NAME"));
                Map<String, Object> convertedTask = convertTaskFields(task);
                convertedTasks.add(convertedTask);
            }
            logger.info("转换后得到{}条任务记录", convertedTasks.size());

            // 如果没有分页参数，返回所有数据
            if (pager == null || pager.getPageSize() <= 0) {
                return convertedTasks;
            }

            // 手动分页处理
            int pageIndex = pager.getPageIndex();
            int pageSize = pager.getPageSize();

            // 前端pageIndex从1开始，需要转换为从0开始的数组索引
            int startIndex = (pageIndex - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, convertedTasks.size());

            logger.info("分页参数: pageIndex={}, pageSize={}, 总记录数={}, startIndex={}, endIndex={}",
                       pageIndex, pageSize, convertedTasks.size(), startIndex, endIndex);

            if (startIndex >= convertedTasks.size()) {
                logger.warn("起始索引超出范围: startIndex={}, 总记录数={}", startIndex, convertedTasks.size());
                return new ArrayList<>();
            }

            List<Map<String, Object>> pagedTasks = convertedTasks.subList(startIndex, endIndex);
            logger.info("分页后返回{}条记录", pagedTasks.size());
            return pagedTasks;
        } catch (Exception e) {
            logger.error("获取项目任务列表失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public int getProjectTaskCount(Map<String, Object> conditions) {
        try {
            return projectTaskMapper.countProjectTasks(conditions);
        } catch (Exception e) {
            logger.error("获取项目任务总数失败", e);
            return 0;
        }
    }
    
    @Override
    public List<Map<String, Object>> searchTasks(String keyword, String projectId, Pager pager) {
        try {
            return projectTaskMapper.searchTasks(keyword, projectId, pager);
        } catch (Exception e) {
            logger.error("搜索任务失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<Map<String, Object>> getTasksByStatus(String projectId, Integer status, Pager pager) {
        try {
            return projectTaskMapper.selectTasksByStatus(projectId, status, pager);
        } catch (Exception e) {
            logger.error("根据状态获取任务失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<Map<String, Object>> getTasksByAssignee(String assignee, Pager pager) {
        try {
            return projectTaskMapper.selectTasksByAssignee(assignee, pager);
        } catch (Exception e) {
            logger.error("根据负责人获取任务失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<Map<String, Object>> getTasksByPriority(String projectId, Integer priority, Pager pager) {
        try {
            return projectTaskMapper.selectTasksByPriority(projectId, priority, pager);
        } catch (Exception e) {
            logger.error("根据优先级获取任务失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public boolean updateTaskStatus(String taskId, Integer status, String userId) {
        try {
            int result = projectTaskMapper.updateTaskStatus(taskId, status, userId);
            return result > 0;
        } catch (Exception e) {
            logger.error("更新任务状态失败", e);
            return false;
        }
    }
    
    @Override
    public boolean updateTaskProgress(String taskId, Integer progress, String userId) {
        try {
            int result = projectTaskMapper.updateTaskProgress(taskId, progress, userId);
            return result > 0;
        } catch (Exception e) {
            logger.error("更新任务进度失败", e);
            return false;
        }
    }
    
    @Override
    public boolean updateTaskAssignee(String taskId, String assignee, String userId) {
        try {
            int result = projectTaskMapper.updateTaskAssignee(taskId, assignee, userId);
            return result > 0;
        } catch (Exception e) {
            logger.error("更新任务负责人失败", e);
            return false;
        }
    }
    
    @Override
    public Map<String, Object> getTaskStatistics(String projectId) {
        try {
            Map<String, Object> statistics = projectTaskMapper.getTaskStatistics(projectId);
            if (statistics == null) {
                statistics = new HashMap<>();
                statistics.put("totalTasks", 0);
                statistics.put("todoTasks", 0);
                statistics.put("inProgressTasks", 0);
                statistics.put("completedTasks", 0);
                statistics.put("cancelledTasks", 0);
                statistics.put("avgProgress", 0.0);
                statistics.put("totalEstimatedHours", 0.0);
                statistics.put("totalActualHours", 0.0);
                statistics.put("overdueTasks", 0);
            }
            return statistics;
        } catch (Exception e) {
            logger.error("获取任务统计失败", e);
            return new HashMap<>();
        }
    }
    
    @Override
    public List<Map<String, Object>> getUserTasks(String userId, Pager pager) {
        try {
            return projectTaskMapper.selectUserTasks(userId, pager);
        } catch (Exception e) {
            logger.error("获取用户任务失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<Map<String, Object>> getSubTasks(String parentTaskId, Pager pager) {
        try {
            return projectTaskMapper.selectSubTasks(parentTaskId, pager);
        } catch (Exception e) {
            logger.error("获取子任务失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public boolean isTaskNameExists(String taskName, String projectId, String excludeId) {
        try {
            int count = projectTaskMapper.checkTaskNameExists(taskName, projectId, excludeId);
            return count > 0;
        } catch (Exception e) {
            logger.error("检查任务名称是否存在失败", e);
            return false;
        }
    }
    
    @Override
    public boolean isTaskCodeExists(String taskCode, String projectId, String excludeId) {
        try {
            int count = projectTaskMapper.checkTaskCodeExists(taskCode, projectId, excludeId);
            return count > 0;
        } catch (Exception e) {
            logger.error("检查任务代码是否存在失败", e);
            return false;
        }
    }
    
    @Override
    public int batchUpdateTaskStatus(List<String> taskIds, Integer status, String userId) {
        try {
            return projectTaskMapper.batchUpdateTaskStatus(taskIds, status, userId);
        } catch (Exception e) {
            logger.error("批量更新任务状态失败", e);
            return 0;
        }
    }
    
    @Override
    public int batchDeleteTasks(List<String> taskIds, String userId) {
        try {
            return projectTaskMapper.batchDeleteTasks(taskIds, userId);
        } catch (Exception e) {
            logger.error("批量删除任务失败", e);
            return 0;
        }
    }
    
    @Override
    public int batchAssignTasks(List<String> taskIds, String assignee, String userId) {
        try {
            return projectTaskMapper.batchAssignTasks(taskIds, assignee, userId);
        } catch (Exception e) {
            logger.error("批量分配任务失败", e);
            return 0;
        }
    }
    
    @Override
    public List<Map<String, Object>> getTaskDependencies(String taskId) {
        try {
            // 简化实现，返回空列表
            // 实际应该查询任务依赖表
            return new ArrayList<>();
        } catch (Exception e) {
            logger.error("获取任务依赖关系失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public boolean addTaskDependency(String taskId, String dependsOnTaskId, String userId) {
        try {
            // 简化实现，返回true
            // 实际应该插入任务依赖表
            return true;
        } catch (Exception e) {
            logger.error("添加任务依赖失败", e);
            return false;
        }
    }
    
    @Override
    public boolean removeTaskDependency(String taskId, String dependsOnTaskId, String userId) {
        try {
            // 简化实现，返回true
            // 实际应该删除任务依赖表记录
            return true;
        } catch (Exception e) {
            logger.error("移除任务依赖失败", e);
            return false;
        }
    }
    
    @Override
    public List<Map<String, Object>> getTaskTimeline(String projectId) {
        try {
            // 简化实现，获取项目任务作为时间线
            Map<String, Object> conditions = new HashMap<>();
            conditions.put("projectId", projectId);
            List<Map<String, Object>> tasks = getProjectTasks(conditions, null);
            
            List<Map<String, Object>> timeline = new ArrayList<>();
            for (Map<String, Object> task : tasks) {
                Map<String, Object> timelineItem = new HashMap<>();
                timelineItem.put("id", task.get("ID"));
                timelineItem.put("name", task.get("TASK_NAME"));
                timelineItem.put("startDate", task.get("PLANNED_START_DATE"));
                timelineItem.put("endDate", task.get("PLANNED_END_DATE"));
                timelineItem.put("progress", task.get("PROGRESS"));
                timelineItem.put("status", task.get("STATUS"));
                timeline.add(timelineItem);
            }
            
            return timeline;
        } catch (Exception e) {
            logger.error("获取任务时间线失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<Map<String, Object>> getTaskGanttData(String projectId) {
        try {
            // 简化实现，返回甘特图数据
            return getTaskTimeline(projectId);
        } catch (Exception e) {
            logger.error("获取任务甘特图数据失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public String copyTask(String taskId, String newTaskName, String userId) {
        try {
            Map<String, Object> sourceTask = getTaskById(taskId);
            if (sourceTask == null) {
                return null;
            }
            
            // 创建新任务
            ProjectTaskEntity newTask = new ProjectTaskEntity();
            newTask.setId(UUID.randomUUID().toString().replace("-", ""));
            newTask.setProjectId((String) sourceTask.get("PROJECT_ID"));
            newTask.setTaskName(newTaskName);
            newTask.setTaskCode((String) sourceTask.get("TASK_CODE") + "_COPY");
            newTask.setDescription((String) sourceTask.get("DESCRIPTION"));
            newTask.setAssignedTo((String) sourceTask.get("ASSIGNEE")); // 使用正确的字段名
            // newTask.setReporter(userId); // ProjectTaskEntity没有这个字段
            newTask.setStartDate((Date) sourceTask.get("PLANNED_START_DATE"));
            newTask.setEndDate((Date) sourceTask.get("PLANNED_END_DATE"));
            newTask.setEstimatedHours((Integer) sourceTask.get("ESTIMATED_HOURS")); // 使用Integer类型
            newTask.setActualHours(0); // 使用Integer类型
            newTask.setStatus(0); // 重置为待办状态
            newTask.setPriority((Integer) sourceTask.get("PRIORITY"));
            newTask.setProgress(0); // 重置进度
            // newTask.setTaskType((Integer) sourceTask.get("TASK_TYPE")); // ProjectTaskEntity没有这个字段
            newTask.setCreator(userId);
            newTask.setCreationDate(new Date());
            newTask.setModifier(userId);
            newTask.setModifiedDate(new Date());
            newTask.setDeleted(0);
            
            return createTask(newTask);
        } catch (Exception e) {
            logger.error("复制任务失败", e);
            return null;
        }
    }
    
    @Override
    public boolean moveTaskToProject(String taskId, String targetProjectId, String userId) {
        try {
            ProjectTaskEntity task = new ProjectTaskEntity();
            task.setId(taskId);
            task.setProjectId(targetProjectId);
            task.setModifier(userId);
            task.setModifiedDate(new Date());
            
            return updateTask(task);
        } catch (Exception e) {
            logger.error("移动任务到其他项目失败", e);
            return false;
        }
    }
    
    @Override
    public List<Map<String, Object>> getTaskWorkLogs(String taskId, Pager pager) {
        try {
            // 简化实现，返回空列表
            // 实际应该查询工作日志表
            return new ArrayList<>();
        } catch (Exception e) {
            logger.error("获取任务工作日志失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public boolean addTaskWorkLog(String taskId, Double workHours, String description, String userId) {
        try {
            // 简化实现，返回true
            // 实际应该插入工作日志表
            return true;
        } catch (Exception e) {
            logger.error("添加任务工作日志失败", e);
            return false;
        }
    }
    
    @Override
    public List<Map<String, Object>> getTaskComments(String taskId, Pager pager) {
        try {
            // 简化实现，返回空列表
            // 实际应该查询评论表
            return new ArrayList<>();
        } catch (Exception e) {
            logger.error("获取任务评论失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public boolean addTaskComment(String taskId, String comment, String userId) {
        try {
            // 简化实现，返回true
            // 实际应该插入评论表
            return true;
        } catch (Exception e) {
            logger.error("添加任务评论失败", e);
            return false;
        }
    }
    
    @Override
    public List<Map<String, Object>> exportTaskData(Map<String, Object> conditions) {
        try {
            // 导出时不分页，获取所有数据
            return projectTaskMapper.selectProjectTasks(conditions, null);
        } catch (Exception e) {
            logger.error("导出任务数据失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<Map<String, Object>> getOverdueTasks(String projectId, Pager pager) {
        try {
            return projectTaskMapper.selectOverdueTasks(projectId, pager);
        } catch (Exception e) {
            logger.error("获取逾期任务失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<Map<String, Object>> getUpcomingTasks(String projectId, Integer days, Pager pager) {
        try {
            return projectTaskMapper.selectUpcomingTasks(projectId, days, pager);
        } catch (Exception e) {
            logger.error("获取即将到期的任务失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 转换数据库字段名为前端期望的字段名
     */
    private Map<String, Object> convertTaskFields(Map<String, Object> dbTask) {
        Map<String, Object> frontendTask = new HashMap<>();

        // 基本字段映射
        frontendTask.put("id", dbTask.get("ID"));
        frontendTask.put("projectId", dbTask.get("PROJECT_ID"));
        frontendTask.put("taskName", dbTask.get("TASK_NAME"));
        frontendTask.put("taskCode", dbTask.get("TASK_CODE"));
        frontendTask.put("description", dbTask.get("DESCRIPTION"));
        frontendTask.put("assignedTo", dbTask.get("ASSIGNEE"));
        frontendTask.put("assignee", dbTask.get("ASSIGNEE")); // 兼容前端字段名
        frontendTask.put("startDate", dbTask.get("PLANNED_START_DATE"));
        frontendTask.put("endDate", dbTask.get("PLANNED_END_DATE"));
        frontendTask.put("estimatedHours", dbTask.get("ESTIMATED_HOURS"));
        frontendTask.put("actualHours", dbTask.get("ACTUAL_HOURS"));
        frontendTask.put("status", dbTask.get("STATUS"));
        frontendTask.put("priority", dbTask.get("PRIORITY"));
        frontendTask.put("progress", dbTask.get("PROGRESS"));
        frontendTask.put("creator", dbTask.get("CREATOR"));
        frontendTask.put("creationDate", dbTask.get("CREATION_DATE"));
        frontendTask.put("modifier", dbTask.get("MODIFIER"));
        frontendTask.put("modifiedDate", dbTask.get("MODIFIED_DATE"));
        frontendTask.put("deleted", dbTask.get("DELETED"));

        // 状态和优先级的文本描述
        Integer status = (Integer) dbTask.get("STATUS");
        if (status != null) {
            frontendTask.put("statusText", getTaskStatusText(status));
        }

        Integer priority = (Integer) dbTask.get("PRIORITY");
        if (priority != null) {
            frontendTask.put("priorityText", getTaskPriorityText(priority));
        }

        return frontendTask;
    }

    /**
     * 获取任务状态文本描述
     */
    private String getTaskStatusText(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case 0: return "待办";
            case 1: return "进行中";
            case 2: return "已完成";
            case 3: return "已取消";
            default: return "未知";
        }
    }

    /**
     * 获取任务优先级文本描述
     */
    private String getTaskPriorityText(Integer priority) {
        if (priority == null) return "中";
        switch (priority) {
            case 1: return "低";
            case 2: return "中";
            case 3: return "高";
            case 4: return "紧急";
            default: return "中";
        }
    }
}
