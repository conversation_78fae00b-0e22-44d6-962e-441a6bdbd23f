package com.ecm.portal.controller;

import com.ecm.common.util.JSONUtils;
import com.ecm.core.entity.Pager;
import com.ecm.core.util.AjaxResult;
import com.ecm.portal.entity.ProjectTaskEntity;
import com.ecm.portal.service.ProjectTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 项目任务管理控制器
 * 兼容MyBatis 2.1.0 + MyBatis-Plus 3.4.2
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/projectTask")
public class ProjectTaskController {
    
    private static final Logger logger = LoggerFactory.getLogger(ProjectTaskController.class);
    
    @Autowired
    private ProjectTaskService projectTaskService;
    
    /**
     * 解析请求参数
     */
    private Map<String, Object> parseRequestBody(String argStr) {
        try {
            if (argStr == null || argStr.trim().isEmpty()) {
                return new HashMap<>();
            }

            Map<String, Object> result = JSONUtils.stringToMap(argStr);

            // 如果解析结果为空，尝试直接解析为JSON对象
            if (result.isEmpty() && argStr.trim().startsWith("{")) {
                result = JSONUtils.stringToMap(argStr);
            }

            logger.debug("解析请求参数成功: {} -> {}", argStr, result);
            return result;
        } catch (Exception e) {
            logger.error("解析请求参数失败: {}", argStr, e);
            return new HashMap<>();
        }
    }
    
    /**
     * 获取项目任务列表
     */
    @PostMapping("/list")
    public AjaxResult getTaskList(@RequestBody(required = false) String argStr) {
        try {
            logger.info("接收到项目任务列表请求，参数: {}", argStr);
            
            Map<String, Object> args = parseRequestBody(argStr);
            logger.info("解析后的参数Map: {}", args);

            String projectId = (String) args.get("projectId");
            logger.info("从projectId字段获取到的值: [{}]", projectId);

            // 如果没有projectId，尝试从其他字段获取
            if (projectId == null || projectId.trim().isEmpty()) {
                projectId = (String) args.get("id");
                logger.info("从id字段获取到的值: [{}]", projectId);
            }
            if (projectId == null || projectId.trim().isEmpty()) {
                projectId = (String) args.get("PROJECT_ID");
                logger.info("从PROJECT_ID字段获取到的值: [{}]", projectId);
            }

            logger.info("最终使用的项目ID: [{}]", projectId);

            if (projectId == null || projectId.trim().isEmpty()) {
                logger.error("项目ID为空，所有参数: {}", args);
                return AjaxResult.error("项目ID不能为空");
            }
            
            // 构建分页参数
            Pager pager = new Pager();
            if (args.get("pageIndex") != null) {
                pager.setPageIndex(Integer.parseInt(args.get("pageIndex").toString()));
            }
            if (args.get("pageSize") != null) {
                pager.setPageSize(Integer.parseInt(args.get("pageSize").toString()));
            }
            
            // 构建查询条件
            Map<String, Object> conditions = new HashMap<>();
            conditions.put("projectId", projectId);

            // 只有当参数不为null且不为空字符串时才添加到查询条件中
            String taskName = (String) args.get("taskName");
            if (taskName != null && !taskName.trim().isEmpty()) {
                conditions.put("taskName", taskName);
            }

            String status = (String) args.get("status");
            if (status != null && !status.trim().isEmpty()) {
                conditions.put("status", status);
            }

            String assignee = (String) args.get("assignee");
            if (assignee != null && !assignee.trim().isEmpty()) {
                conditions.put("assignee", assignee);
            }

            String priority = (String) args.get("priority");
            if (priority != null && !priority.trim().isEmpty()) {
                conditions.put("priority", priority);
            }

            logger.info("构建的查询条件: {}", conditions);
            
            // 获取任务列表
            List<Map<String, Object>> taskList = projectTaskService.getProjectTasks(conditions, pager);
            
            // 获取总数
            int totalCount = projectTaskService.getProjectTaskCount(conditions);
            // 注意：根据现有代码，Pager可能没有setTotalCount方法，使用其他方式设置总数
            
            AjaxResult result = AjaxResult.success();
            result.put("data", taskList);
            result.put("pager", pager);
            
            logger.info("返回项目任务列表，共{}条记录", taskList.size());
            
            return result;
            
        } catch (Exception e) {
            logger.error("获取项目任务列表失败", e);
            return AjaxResult.error("获取项目任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据项目ID获取任务列表（支持URL路径参数）
     */
    @PostMapping("/list/{projectId}")
    public AjaxResult getTaskListByProjectId(@PathVariable String projectId, @RequestBody(required = false) String argStr) {
        try {
            logger.info("接收到项目任务列表请求，项目ID: {}, 参数: {}", projectId, argStr);

            if (projectId == null || projectId.trim().isEmpty()) {
                return AjaxResult.error("项目ID不能为空");
            }

            Map<String, Object> args = parseRequestBody(argStr);

            // 构建分页参数
            Pager pager = new Pager();
            if (args.get("pageIndex") != null) {
                pager.setPageIndex(Integer.parseInt(args.get("pageIndex").toString()));
            }
            if (args.get("pageSize") != null) {
                pager.setPageSize(Integer.parseInt(args.get("pageSize").toString()));
            }

            // 构建查询条件
            Map<String, Object> conditions = new HashMap<>();
            conditions.put("projectId", projectId);

            // 其他查询条件
            if (args.get("taskName") != null && !args.get("taskName").toString().trim().isEmpty()) {
                conditions.put("taskName", args.get("taskName"));
            }
            if (args.get("status") != null) {
                conditions.put("status", args.get("status"));
            }
            if (args.get("assignee") != null && !args.get("assignee").toString().trim().isEmpty()) {
                conditions.put("assignee", args.get("assignee"));
            }
            if (args.get("priority") != null) {
                conditions.put("priority", args.get("priority"));
            }

            // 获取任务列表
            List<Map<String, Object>> taskList = projectTaskService.getProjectTasks(conditions, pager);

            // 获取总数
            int totalCount = projectTaskService.getProjectTaskCount(conditions);

            AjaxResult result = AjaxResult.success();
            result.put("data", taskList);
            result.put("pager", pager);

            logger.info("返回项目任务列表，项目ID: {}, 共{}条记录", projectId, taskList.size());

            return result;

        } catch (Exception e) {
            logger.error("获取项目任务列表失败", e);
            return AjaxResult.error("获取项目任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务详情
     */
    @PostMapping("/detail")
    public AjaxResult getTaskDetail(@RequestBody(required = false) String argStr) {
        try {
            logger.info("接收到任务详情请求，参数: {}", argStr);
            
            Map<String, Object> args = parseRequestBody(argStr);
            String taskId = (String) args.get("id");
            
            if (taskId == null || taskId.trim().isEmpty()) {
                return AjaxResult.error("任务ID不能为空");
            }
            
            Map<String, Object> task = projectTaskService.getTaskById(taskId);
            
            if (task == null) {
                return AjaxResult.error("任务不存在");
            }
            
            AjaxResult result = AjaxResult.success();
            result.put("data", task);
            
            logger.info("返回任务详情，任务ID: {}", taskId);
            
            return result;
            
        } catch (Exception e) {
            logger.error("获取任务详情失败", e);
            return AjaxResult.error("获取任务详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建任务
     */
    @PostMapping("/create")
    public AjaxResult createTask(@RequestBody(required = false) String argStr) {
        try {
            logger.info("接收到创建任务请求，参数: {}", argStr);
            
            Map<String, Object> args = parseRequestBody(argStr);
            
            // 验证必填字段
            String projectId = (String) args.get("projectId");
            String taskName = (String) args.get("taskName");
            
            if (projectId == null || projectId.trim().isEmpty()) {
                return AjaxResult.error("项目ID不能为空");
            }
            if (taskName == null || taskName.trim().isEmpty()) {
                return AjaxResult.error("任务名称不能为空");
            }
            
            // 构建任务实体
            ProjectTaskEntity task = new ProjectTaskEntity();
            task.setId(UUID.randomUUID().toString().replace("-", ""));
            task.setProjectId(projectId);
            task.setTaskName(taskName);
            task.setTaskCode((String) args.get("taskCode"));
            task.setDescription((String) args.get("description"));
            task.setAssignedTo((String) args.get("assignee")); // 使用正确的字段名
            // task.setReporter((String) args.get("reporter")); // ProjectTaskEntity没有这个字段
            task.setStartDate((Date) args.get("startDate"));
            task.setEndDate((Date) args.get("endDate"));
            task.setEstimatedHours(args.get("estimatedHours") != null ?
                Integer.parseInt(args.get("estimatedHours").toString()) : null); // 使用Integer类型
            task.setActualHours(0); // 使用Integer类型
            task.setStatus(args.get("status") != null ? Integer.parseInt(args.get("status").toString()) : 0);
            task.setPriority(args.get("priority") != null ? Integer.parseInt(args.get("priority").toString()) : 2);
            task.setProgress(0);
            // task.setParentTaskId((String) args.get("parentTaskId")); // ProjectTaskEntity没有这个字段
            // task.setTaskType(args.get("taskType") != null ? Integer.parseInt(args.get("taskType").toString()) : 1); // ProjectTaskEntity没有这个字段
            task.setCreator("admin"); // 这里应该从session获取当前用户
            task.setCreationDate(new Date());
            task.setModifier("admin");
            task.setModifiedDate(new Date());
            task.setDeleted(0);
            
            String taskId = projectTaskService.createTask(task);
            
            if (taskId != null) {
                AjaxResult result = AjaxResult.success("创建任务成功");
                result.put("taskId", taskId);
                
                logger.info("创建任务成功，任务ID: {}", taskId);
                
                return result;
            } else {
                return AjaxResult.error("创建任务失败");
            }
            
        } catch (Exception e) {
            logger.error("创建任务失败", e);
            return AjaxResult.error("创建任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新任务
     */
    @PostMapping("/update")
    public AjaxResult updateTask(@RequestBody(required = false) String argStr) {
        try {
            logger.info("接收到更新任务请求，参数: {}", argStr);
            
            Map<String, Object> args = parseRequestBody(argStr);
            String taskId = (String) args.get("id");
            
            if (taskId == null || taskId.trim().isEmpty()) {
                return AjaxResult.error("任务ID不能为空");
            }
            
            // 获取现有任务
            Map<String, Object> existingTask = projectTaskService.getTaskById(taskId);
            if (existingTask == null) {
                return AjaxResult.error("任务不存在");
            }
            
            // 构建更新实体
            ProjectTaskEntity task = new ProjectTaskEntity();
            task.setId(taskId);
            task.setTaskName((String) args.get("taskName"));
            task.setTaskCode((String) args.get("taskCode"));
            task.setDescription((String) args.get("description"));
            task.setAssignedTo((String) args.get("assignee")); // 使用正确的字段名
            // task.setReporter((String) args.get("reporter")); // ProjectTaskEntity没有这个字段
            task.setStartDate((Date) args.get("startDate"));
            task.setEndDate((Date) args.get("endDate"));
            task.setEstimatedHours(args.get("estimatedHours") != null ?
                Integer.parseInt(args.get("estimatedHours").toString()) : null); // 使用Integer类型
            task.setActualHours(args.get("actualHours") != null ?
                Integer.parseInt(args.get("actualHours").toString()) : null); // 使用Integer类型
            task.setStatus(args.get("status") != null ? Integer.parseInt(args.get("status").toString()) : null);
            task.setPriority(args.get("priority") != null ? Integer.parseInt(args.get("priority").toString()) : null);
            task.setProgress(args.get("progress") != null ? Integer.parseInt(args.get("progress").toString()) : null);
            // task.setTaskType(args.get("taskType") != null ? Integer.parseInt(args.get("taskType").toString()) : null); // ProjectTaskEntity没有这个字段
            task.setModifier("admin"); // 这里应该从session获取当前用户
            task.setModifiedDate(new Date());
            
            boolean success = projectTaskService.updateTask(task);
            
            if (success) {
                logger.info("更新任务成功，任务ID: {}", taskId);
                return AjaxResult.success("更新任务成功");
            } else {
                return AjaxResult.error("更新任务失败");
            }
            
        } catch (Exception e) {
            logger.error("更新任务失败", e);
            return AjaxResult.error("更新任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除任务
     */
    @PostMapping("/delete")
    public AjaxResult deleteTask(@RequestBody(required = false) String argStr) {
        try {
            logger.info("接收到删除任务请求，参数: {}", argStr);
            
            Map<String, Object> args = parseRequestBody(argStr);
            String taskId = (String) args.get("id");
            
            if (taskId == null || taskId.trim().isEmpty()) {
                return AjaxResult.error("任务ID不能为空");
            }
            
            boolean success = projectTaskService.deleteTask(taskId);
            
            if (success) {
                logger.info("删除任务成功，任务ID: {}", taskId);
                return AjaxResult.success("删除任务成功");
            } else {
                return AjaxResult.error("删除任务失败");
            }
            
        } catch (Exception e) {
            logger.error("删除任务失败", e);
            return AjaxResult.error("删除任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新任务状态
     */
    @PostMapping("/updateStatus")
    public AjaxResult updateTaskStatus(@RequestBody(required = false) String argStr) {
        try {
            logger.info("接收到更新任务状态请求，参数: {}", argStr);
            
            Map<String, Object> args = parseRequestBody(argStr);
            String taskId = (String) args.get("id");
            Integer status = args.get("status") != null ? Integer.parseInt(args.get("status").toString()) : null;
            
            if (taskId == null || status == null) {
                return AjaxResult.error("任务ID和状态不能为空");
            }
            
            boolean success = projectTaskService.updateTaskStatus(taskId, status, "admin");
            
            if (success) {
                logger.info("更新任务状态成功，任务ID: {}, 状态: {}", taskId, status);
                return AjaxResult.success("更新任务状态成功");
            } else {
                return AjaxResult.error("更新任务状态失败");
            }
            
        } catch (Exception e) {
            logger.error("更新任务状态失败", e);
            return AjaxResult.error("更新任务状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新任务进度
     */
    @PostMapping("/updateProgress")
    public AjaxResult updateTaskProgress(@RequestBody(required = false) String argStr) {
        try {
            logger.info("接收到更新任务进度请求，参数: {}", argStr);
            
            Map<String, Object> args = parseRequestBody(argStr);
            String taskId = (String) args.get("id");
            Integer progress = args.get("progress") != null ? Integer.parseInt(args.get("progress").toString()) : null;
            
            if (taskId == null || progress == null) {
                return AjaxResult.error("任务ID和进度不能为空");
            }
            
            boolean success = projectTaskService.updateTaskProgress(taskId, progress, "admin");
            
            if (success) {
                logger.info("更新任务进度成功，任务ID: {}, 进度: {}%", taskId, progress);
                return AjaxResult.success("更新任务进度成功");
            } else {
                return AjaxResult.error("更新任务进度失败");
            }
            
        } catch (Exception e) {
            logger.error("更新任务进度失败", e);
            return AjaxResult.error("更新任务进度失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取任务统计信息
     */
    @PostMapping("/statistics")
    public AjaxResult getTaskStatistics(@RequestBody(required = false) String argStr) {
        try {
            logger.info("接收到任务统计请求，参数: {}", argStr);
            
            Map<String, Object> args = parseRequestBody(argStr);
            String projectId = (String) args.get("projectId");
            
            if (projectId == null || projectId.trim().isEmpty()) {
                return AjaxResult.error("项目ID不能为空");
            }
            
            Map<String, Object> statistics = projectTaskService.getTaskStatistics(projectId);
            
            AjaxResult result = AjaxResult.success();
            result.put("data", statistics);
            
            logger.info("返回任务统计信息");
            
            return result;
            
        } catch (Exception e) {
            logger.error("获取任务统计失败", e);
            return AjaxResult.error("获取任务统计失败: " + e.getMessage());
        }
    }
}
