package com.ecm.portal.mapper;

import com.ecm.core.entity.Pager;
import com.ecm.portal.entity.ProjectEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 项目数据访问层
 * 使用XML配置避免MyBatis版本冲突
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Repository
@Mapper
public interface ProjectMapper {

    /**
     * 插入项目
     */
    int insertProject(ProjectEntity project);

    /**
     * 更新项目
     */
    int updateProject(ProjectEntity project);

    /**
     * 软删除项目
     */
    int deleteProject(@Param("projectId") String projectId);

    /**
     * 根据ID查询项目
     */
    Map<String, Object> selectProjectById(@Param("projectId") String projectId);

    /**
     * 查询项目列表
     */
    List<Map<String, Object>> selectProjectList(@Param("conditions") Map<String, Object> conditions, @Param("pager") Pager pager);

    /**
     * 统计项目数量
     */
    int countProjects(@Param("conditions") Map<String, Object> conditions);

    /**
     * 搜索项目
     */
    List<Map<String, Object>> searchProjects(@Param("keyword") String keyword, @Param("pager") Pager pager);

    /**
     * 根据状态查询项目
     */
    List<Map<String, Object>> selectProjectsByStatus(@Param("status") Integer status, @Param("pager") Pager pager);

    /**
     * 根据项目经理查询项目
     */
    List<Map<String, Object>> selectProjectsByManager(@Param("managerId") String managerId, @Param("pager") Pager pager);

    /**
     * 更新项目状态
     */
    int updateProjectStatus(@Param("projectId") String projectId, @Param("status") Integer status, @Param("userId") String userId);

    /**
     * 更新项目进度
     */
    int updateProjectProgress(@Param("projectId") String projectId, @Param("progress") Integer progress, @Param("userId") String userId);

    /**
     * 检查项目名称是否存在
     */
    int checkProjectNameExists(@Param("projectName") String projectName, @Param("excludeId") String excludeId);

    /**
     * 检查项目代码是否存在
     */
    int checkProjectCodeExists(@Param("projectCode") String projectCode, @Param("excludeId") String excludeId);

    /**
     * 获取项目统计信息
     */
    Map<String, Object> getProjectStatistics();

    /**
     * 获取用户参与的项目
     */
    List<Map<String, Object>> selectUserProjects(@Param("userId") String userId, @Param("pager") Pager pager);

    /**
     * 批量更新项目状态
     */
    int batchUpdateProjectStatus(@Param("projectIds") List<String> projectIds, @Param("status") Integer status, @Param("userId") String userId);

    /**
     * 批量删除项目
     */
    int batchDeleteProjects(@Param("projectIds") List<String> projectIds, @Param("userId") String userId);

    /**
     * 归档项目
     */
    int archiveProject(@Param("projectId") String projectId, @Param("userId") String userId);
}
