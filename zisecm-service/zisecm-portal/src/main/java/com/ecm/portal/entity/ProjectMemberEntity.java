package com.ecm.portal.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * 项目成员实体类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ProjectMemberEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 成员ID
     */
    private String id;
    
    /**
     * 项目ID
     */
    private String projectId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 用户姓名
     */
    private String displayName;
    
    /**
     * 用户邮箱
     */
    private String email;
    
    /**
     * 用户电话
     */
    private String phone;
    
    /**
     * 部门
     */
    private String department;
    
    /**
     * 职位
     */
    private String position;
    
    /**
     * 项目角色：1-项目经理，2-开发人员，3-测试人员，4-设计人员，5-产品经理，6-其他
     */
    private Integer role;
    
    /**
     * 角色名称
     */
    private String roleName;
    
    /**
     * 权限级别：1-查看，2-编辑，3-管理，4-所有权限
     */
    private Integer permissionLevel;
    
    /**
     * 加入日期
     */
    private Date joinDate;
    
    /**
     * 离开日期
     */
    private Date leaveDate;
    
    /**
     * 成员状态：0-待确认，1-活跃，2-暂停，3-已离开
     */
    private Integer status;
    
    /**
     * 工作量分配百分比
     */
    private Integer workloadPercent;
    
    /**
     * 小时费率
     */
    private Double hourlyRate;
    
    /**
     * 备注
     */
    private String remarks;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 创建时间
     */
    private Date creationDate;
    
    /**
     * 修改人
     */
    private String modifier;
    
    /**
     * 修改时间
     */
    private Date modifiedDate;
    
    /**
     * 是否删除：0-否，1-是
     */
    private Integer deleted;
    
    // 构造函数
    public ProjectMemberEntity() {}
    
    public ProjectMemberEntity(String projectId, String userId, String username, Integer role) {
        this.projectId = projectId;
        this.userId = userId;
        this.username = username;
        this.role = role;
        this.status = 0; // 默认待确认
        this.permissionLevel = 1; // 默认查看权限
        this.workloadPercent = 100; // 默认100%工作量
        this.joinDate = new Date();
        this.creationDate = new Date();
        this.modifiedDate = new Date();
        this.deleted = 0;
    }
    
    // Getter和Setter方法
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getProjectId() {
        return projectId;
    }
    
    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }
    
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getDepartment() {
        return department;
    }
    
    public void setDepartment(String department) {
        this.department = department;
    }
    
    public String getPosition() {
        return position;
    }
    
    public void setPosition(String position) {
        this.position = position;
    }
    
    public Integer getRole() {
        return role;
    }
    
    public void setRole(Integer role) {
        this.role = role;
    }
    
    public String getRoleName() {
        return roleName;
    }
    
    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }
    
    public Integer getPermissionLevel() {
        return permissionLevel;
    }
    
    public void setPermissionLevel(Integer permissionLevel) {
        this.permissionLevel = permissionLevel;
    }
    
    public Date getJoinDate() {
        return joinDate;
    }
    
    public void setJoinDate(Date joinDate) {
        this.joinDate = joinDate;
    }
    
    public Date getLeaveDate() {
        return leaveDate;
    }
    
    public void setLeaveDate(Date leaveDate) {
        this.leaveDate = leaveDate;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Integer getWorkloadPercent() {
        return workloadPercent;
    }
    
    public void setWorkloadPercent(Integer workloadPercent) {
        this.workloadPercent = workloadPercent;
    }
    
    public Double getHourlyRate() {
        return hourlyRate;
    }
    
    public void setHourlyRate(Double hourlyRate) {
        this.hourlyRate = hourlyRate;
    }
    
    public String getRemarks() {
        return remarks;
    }
    
    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
    
    public String getCreator() {
        return creator;
    }
    
    public void setCreator(String creator) {
        this.creator = creator;
    }
    
    public Date getCreationDate() {
        return creationDate;
    }
    
    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }
    
    public String getModifier() {
        return modifier;
    }
    
    public void setModifier(String modifier) {
        this.modifier = modifier;
    }
    
    public Date getModifiedDate() {
        return modifiedDate;
    }
    
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }
    
    public Integer getDeleted() {
        return deleted;
    }
    
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }
    
    /**
     * 获取角色文字描述
     */
    public String getRoleText() {
        if (role == null) return "未知";
        switch (role) {
            case 1: return "项目经理";
            case 2: return "开发人员";
            case 3: return "测试人员";
            case 4: return "设计人员";
            case 5: return "产品经理";
            case 6: return "其他";
            default: return "未知";
        }
    }
    
    /**
     * 获取权限级别文字描述
     */
    public String getPermissionLevelText() {
        if (permissionLevel == null) return "未知";
        switch (permissionLevel) {
            case 1: return "查看";
            case 2: return "编辑";
            case 3: return "管理";
            case 4: return "所有权限";
            default: return "未知";
        }
    }
    
    /**
     * 获取状态文字描述
     */
    public String getStatusText() {
        if (status == null) return "未知";
        switch (status) {
            case 0: return "待确认";
            case 1: return "活跃";
            case 2: return "暂停";
            case 3: return "已离开";
            default: return "未知";
        }
    }
    
    @Override
    public String toString() {
        return "ProjectMemberEntity{" +
                "id='" + id + '\'' +
                ", projectId='" + projectId + '\'' +
                ", userId='" + userId + '\'' +
                ", username='" + username + '\'' +
                ", displayName='" + displayName + '\'' +
                ", role=" + role +
                ", status=" + status +
                '}';
    }
} 