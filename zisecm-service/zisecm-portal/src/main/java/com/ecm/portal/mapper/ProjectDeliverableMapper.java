package com.ecm.portal.mapper;

import com.ecm.core.entity.Pager;
import com.ecm.portal.entity.ProjectDeliverableEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 项目交付物数据访问接口
 * 兼容MyBatis 2.1.0 + MyBatis-Plus 3.4.2
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ProjectDeliverableMapper {
    
    /**
     * 插入交付物
     * @param deliverable 交付物实体
     * @return 影响行数
     */
    int insertProjectDeliverable(ProjectDeliverableEntity deliverable);

    /**
     * 更新交付物
     * @param deliverable 交付物实体
     * @return 影响行数
     */
    int updateProjectDeliverable(ProjectDeliverableEntity deliverable);

    /**
     * 删除交付物（软删除）
     * @param deliverableId 交付物ID
     * @return 影响行数
     */
    int deleteProjectDeliverable(@Param("deliverableId") String deliverableId);

    /**
     * 根据ID查询交付物
     * @param deliverableId 交付物ID
     * @return 交付物信息
     */
    ProjectDeliverableEntity selectProjectDeliverableById(@Param("deliverableId") String deliverableId);
    
    /**
     * 查询交付物列表
     * @param conditions 查询条件
     * @param pager 分页参数
     * @return 交付物列表
     */
    List<Map<String, Object>> selectDeliverableList(@Param("conditions") Map<String, Object> conditions, @Param("pager") Pager pager);
    
    /**
     * 查询交付物总数
     * @param conditions 查询条件
     * @return 总数
     */
    int selectDeliverableCount(@Param("conditions") Map<String, Object> conditions);
    
    /**
     * 根据任务ID查询交付物列表
     * @param taskId 任务ID
     * @param pager 分页参数
     * @return 交付物列表
     */
    List<Map<String, Object>> selectDeliverablesByTaskId(@Param("taskId") String taskId, @Param("pager") Pager pager);
    
    /**
     * 根据任务ID查询交付物总数
     * @param taskId 任务ID
     * @return 总数
     */
    int selectDeliverableCountByTaskId(@Param("taskId") String taskId);
    
    /**
     * 根据项目ID查询交付物列表
     * @param projectId 项目ID
     * @param pager 分页参数
     * @return 交付物列表
     */
    List<Map<String, Object>> selectDeliverablesByProjectId(@Param("projectId") String projectId, @Param("pager") Pager pager);
    
    /**
     * 根据负责人查询交付物列表
     * @param assignee 负责人ID
     * @param pager 分页参数
     * @return 交付物列表
     */
    List<Map<String, Object>> selectDeliverablesByAssignee(@Param("assignee") String assignee, @Param("pager") Pager pager);
    
    /**
     * 根据状态查询交付物列表
     * @param status 状态
     * @param pager 分页参数
     * @return 交付物列表
     */
    List<Map<String, Object>> selectDeliverablesByStatus(@Param("status") Integer status, @Param("pager") Pager pager);
    
    /**
     * 搜索交付物
     * @param keyword 关键词
     * @param taskId 任务ID（可选）
     * @param pager 分页参数
     * @return 交付物列表
     */
    List<Map<String, Object>> searchDeliverables(@Param("keyword") String keyword, @Param("taskId") String taskId, @Param("pager") Pager pager);
    
    /**
     * 更新交付物状态
     * @param deliverableId 交付物ID
     * @param status 状态
     * @param lastModifiedBy 修改人
     * @return 影响行数
     */
    int updateDeliverableStatus(@Param("deliverableId") String deliverableId, @Param("status") Integer status, @Param("lastModifiedBy") String lastModifiedBy);
    
    /**
     * 更新交付物进度
     * @param deliverableId 交付物ID
     * @param progress 进度
     * @param lastModifiedBy 修改人
     * @return 影响行数
     */
    int updateDeliverableProgress(@Param("deliverableId") String deliverableId, @Param("progress") Integer progress, @Param("lastModifiedBy") String lastModifiedBy);
    
    /**
     * 更新交付物文件信息
     * @param deliverableId 交付物ID
     * @param filePath 文件路径
     * @param fileName 文件名称
     * @param fileSize 文件大小
     * @param lastModifiedBy 修改人
     * @return 影响行数
     */
    int updateDeliverableFile(@Param("deliverableId") String deliverableId, @Param("filePath") String filePath, 
                             @Param("fileName") String fileName, @Param("fileSize") Long fileSize, @Param("lastModifiedBy") String lastModifiedBy);
    
    /**
     * 批量更新交付物状态
     * @param deliverableIds 交付物ID列表
     * @param status 状态
     * @param lastModifiedBy 修改人
     * @return 影响行数
     */
    int batchUpdateDeliverableStatus(@Param("deliverableIds") List<String> deliverableIds, @Param("status") Integer status, @Param("lastModifiedBy") String lastModifiedBy);
    
    /**
     * 获取交付物统计信息
     * @param taskId 任务ID
     * @return 统计信息
     */
    Map<String, Object> getDeliverableStatistics(@Param("taskId") String taskId);
    
    /**
     * 获取项目交付物统计信息
     * @param projectId 项目ID
     * @return 统计信息
     */
    Map<String, Object> getProjectDeliverableStatistics(@Param("projectId") String projectId);
    
    /**
     * 获取逾期交付物列表
     * @param taskId 任务ID（可选）
     * @param pager 分页参数
     * @return 逾期交付物列表
     */
    List<Map<String, Object>> selectOverdueDeliverables(@Param("taskId") String taskId, @Param("pager") Pager pager);
    
    /**
     * 获取即将到期的交付物列表
     * @param days 天数
     * @param taskId 任务ID（可选）
     * @param pager 分页参数
     * @return 即将到期的交付物列表
     */
    List<Map<String, Object>> selectUpcomingDeliverables(@Param("days") Integer days, @Param("taskId") String taskId, @Param("pager") Pager pager);
    
    /**
     * 检查交付物编码是否存在
     * @param deliverableCode 交付物编码
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 数量
     */
    int checkDeliverableCodeExists(@Param("deliverableCode") String deliverableCode, @Param("excludeId") String excludeId);
    
    /**
     * 获取任务的交付物进度汇总
     * @param taskId 任务ID
     * @return 进度汇总
     */
    Map<String, Object> getTaskDeliverableProgress(@Param("taskId") String taskId);
    
    /**
     * 根据类型统计交付物
     * @param taskId 任务ID（可选）
     * @return 类型统计
     */
    List<Map<String, Object>> getDeliverableTypeStatistics(@Param("taskId") String taskId);
    
    /**
     * 获取交付物时间线数据
     * @param taskId 任务ID
     * @return 时间线数据
     */
    List<Map<String, Object>> getDeliverableTimeline(@Param("taskId") String taskId);


}
