package com.ecm.portal;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.ecm.core.db.PageInterceptor;
import com.ecm.core.db.oracle.Clob2StringHandle;
import com.ecm.core.util.UserSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.env.Environment;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;


@SpringBootApplication	(exclude = {DataSourceAutoConfiguration.class })
@ComponentScan({ /* "com.ecm", */"com.ecm.core.dao",
		"com.ecm.core.db","com.ecm.core.entity",
		"com.ecm.core.bpm","com.ecm.core.service",
		"com.ecm.core.cache.*","com.ecm.core.util",
		"com.ecm.portal.*","com.ecm.flowable",
		"com.ecm.core.sync","com.ecm.km.*","com.ecm.km",
		"com.ecm.common.dataexchange.func.*","com.ecm.dcms.*",
		"com.ecm.wopiserver","com.ecm.archive4check"
		/*,"org.flowable.ui.modeler","org.flowable.ui.common"*/})
@MapperScan({"com.ecm.core.dao","com.ecm.portal.mapper","com.ecm.km.mapper"})
@EnableTransactionManagement//(proxyTargetClass = true)
@EnableScheduling   // 开启定时任务
public class ZisECMApplication extends SpringBootServletInitializer{
	@Value("${posyspath}")
	private String poSysPath;

	@Autowired
	private PageInterceptor pageInterceptor;

//    @Override
//    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
//        return application.sources(ZisECMApplication.class);
//    }

    public static void main(String[] args) throws Exception {
        ApplicationContext app= SpringApplication.run(ZisECMApplication.class, args);
		System.out.println("[.. [.. [..       [.. [..  [..       [..\n" +
							"[..          [..           [. [..   [...\n" +
							"[..          [..           [.. [.. [ [..\n" +
							"[.. [.. [..  [.            [..  [..  [..\n" +
							"[..          [..           [..   [.  [..\n" +
							"[..          [..           [..       [..\n" +
							"[.. [.. [..      [.. [..   [..       [..\n" +
							"                        ");
		System.out.println("----->  ZisEcm Portal Started successfully  <----- ");
        //SpringUtil.setAppcxt(app);
    }
    /*
    @Bean
    public EmbeddedServletContainerCustomizer embeddedServletContainerCustomizer() {
	    return new EmbeddedServletContainerCustomizer() {
		    @Override
		    public void customize(ConfigurableEmbeddedServletContainer container) {
		    //设置session超时时间为20分钟
		    container.setSessionTimeout(20, TimeUnit.MINUTES);
		    }
	    };
    }
    */
/*
	@Bean
	public MultipartConfigElement multipartConfigElement() {
		MultipartConfigFactory factory = new MultipartConfigFactory();
		//文件最大5GB,DataUnit提供5中类型B,KB,MB,GB,TB
		factory.setMaxFileSize(DataSize.of(5, DataUnit.GIGABYTES));
		/// 设置总上传数据总大小10GB
		factory.setMaxRequestSize(DataSize.of(10, DataUnit.GIGABYTES));
		return factory.createMultipartConfig();
	}
 */

    @Bean
    public PlatformTransactionManager txManager(DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

	@Bean
	public ThreadPoolTaskScheduler threadPoolTaskScheduler() {
		return new ThreadPoolTaskScheduler();
	}

//    @Bean
//    public FilterRegistrationBean loginFilterRegistration() {
//
//      FilterRegistrationBean registration = new FilterRegistrationBean();
//      registration.setFilter(new LoginFilter());
//      registration.addUrlPatterns(env.getProperty("web.filter.pattern"));
//      //registration.addInitParameter("paramName", "paramValue");
//      //registration.setName("testFilter");
//      //registration.setOrder(1);
//      return registration;
//    }

	@Autowired
	private Environment env;

	//destroy-method="close"的作用是当数据库连接不使用的时候,就把该连接重新放到数据池中,方便下次使用调用.
	@Bean
	public DataSource dataSource() {
		DruidDataSource dataSource = new DruidDataSource();
		dataSource.setUrl(env.getProperty("spring.datasource.url"));
		dataSource.setUsername(env.getProperty("spring.datasource.username"));//用户名
		dataSource.setPassword(env.getProperty("spring.datasource.password"));//密码
		dataSource.setDriverClassName(env.getProperty("spring.datasource.driver-class-name"));
		dataSource.setInitialSize(2);//初始化时建立物理连接的个数
		dataSource.setMaxActive(60);//最大连接池数量
		dataSource.setMinIdle(0);//最小连接池数量
		dataSource.setMaxWait(60000);//获取连接时最大等待时间，单位毫秒。
		dataSource.setTimeBetweenEvictionRunsMillis(60000);
		dataSource.setValidationQuery(env.getProperty("spring.datasource.validationQuery"));//用来检测连接是否有效的sql
		dataSource.setTestOnBorrow(false);//申请连接时执行validationQuery检测连接是否有效
		dataSource.setTestWhileIdle(true);//建议配置为true，不影响性能，并且保证安全性。
		dataSource.setPoolPreparedStatements(false);//是否缓存preparedStatement，也就是PSCache
		return dataSource;
	}

//	@Bean
//	public MybatisPlusInterceptor mybatisPlusInterceptor() {
//		MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
//		interceptor.addInnerInterceptor(new PageInterceptor(DbType.ORACLE));
//		return interceptor;
//	}
//
//	@Bean
//	public PaginationInterceptor paginationInterceptor() {
//		PaginationInterceptor paginationInterceptor = new PageInterceptor();
//		paginationInterceptor.setDialectType(DbType.ORACLE.getDb());
//		return paginationInterceptor;
//
//
//	}


	@Bean("sqlSessionFactory")
	public SqlSessionFactory sqlSessionFactory() throws Exception {
		MybatisSqlSessionFactoryBean sqlSessionFactory = new MybatisSqlSessionFactoryBean();
		sqlSessionFactory.setDataSource(dataSource());
		/**
		 * 下面这一句setMapperLocations必须加，不然会报错：invalid bound statement (not found)问题
		 */
		sqlSessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*.xml"));
		MybatisConfiguration configuration = new MybatisConfiguration();
		configuration.setJdbcTypeForNull(JdbcType.NULL);
		configuration.setMapUnderscoreToCamelCase(true);
		configuration.setCacheEnabled(false);
		configuration.setCallSettersOnNulls(true);
		sqlSessionFactory.setConfiguration(configuration);
		//注册自定义Handler
		sqlSessionFactory.setTypeHandlers(new Clob2StringHandle());
		//PerformanceInterceptor(),OptimisticLockerInterceptor()
		//添加分页功能
		sqlSessionFactory.setPlugins(pageInterceptor);
//        sqlSessionFactory.setGlobalConfig(globalConfiguration());
		return sqlSessionFactory.getObject();
	}

	/**
	 * 获取当前登陆用户的具体信息
	 * @return 存放当前登陆用户的对象
	 */
	@Bean
	public UserSession getCurrentUserSession(){
		return new UserSession();
	}

	@Bean
	public ServletRegistrationBean pageofficeRegistrationBean()  {
		com.zhuozhengsoft.pageoffice.poserver.Server poserver = new com.zhuozhengsoft.pageoffice.poserver.Server();
/**如果当前项目是打成jar或者war包运行，强烈建议将license的路径更换成某个固定的绝对路径下，不要放当前项目文件夹下,为了防止每次重新发布项目导致license丢失问题。
 * 比如windows服务器下：D:/pageoffice，linux服务器下:/root/pageoffice
 */
//设置PageOffice注册成功后,license.lic文件存放的目录
		poserver.setSysPath(poSysPath);//poSysPath可以在application.properties这个文件中配置，也可以直设置文件夹路径，比如：poserver.setSysPath("D:/pageoffice");
		ServletRegistrationBean srb = new ServletRegistrationBean(poserver);
		srb.addUrlMappings("/poserver.zz");
		srb.addUrlMappings("/poclient");
		srb.addUrlMappings("/pageoffice.js");
		srb.addUrlMappings("/sealsetup.exe");
		return srb;
	}

}
