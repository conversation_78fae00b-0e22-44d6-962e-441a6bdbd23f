-- 测试项目数据
-- 创建时间：2024-01-01

-- 插入测试项目
INSERT INTO PROJECT (
    ID, PROJECT_NAME, PROJECT_CODE, DESCRIPTION, STATUS, PRIORITY,
    PROJECT_MANAGER, PROJECT_MANAGER_NAME, START_DATE, END_DATE,
    PLANNED_START_DATE, PLANNED_END_DATE, PROGRESS, BUDGET, ACTUAL_COST,
    CREATOR, CREATION_DATE, MODIFIER, MODIFIED_DATE, DELETED,
    PROJECT_TYPE, DEPARTMENT, TAGS, DOCUMENT_PATH, ARCHIVED
) VALUES (
    'TEST_PROJECT_001',
    '档案管理系统升级项目',
    'ARCHIVE_UPGRADE_2024',
    '对现有档案管理系统进行功能升级和优化，包括用户界面改进、性能优化、新功能开发等。',
    1, -- 进行中
    3, -- 高优先级
    'admin',
    '系统管理员',
    '2024-01-15 09:00:00',
    NULL, -- 实际结束时间待定
    '2024-01-01 09:00:00', -- 计划开始时间
    '2024-06-30 18:00:00', -- 计划结束时间
    35, -- 当前进度35%
    500000.00, -- 预算50万
    150000.00, -- 已花费15万
    'admin',
    NOW(),
    'admin',
    NOW(),
    0, -- 未删除
    'SYSTEM', -- 系统集成项目
    '信息技术部',
    '档案管理,系统升级,用户体验',
    '/projects/archive_upgrade_2024',
    0 -- 未归档
);

-- 插入第二个测试项目
INSERT INTO PROJECT (
    ID, PROJECT_NAME, PROJECT_CODE, DESCRIPTION, STATUS, PRIORITY,
    PROJECT_MANAGER, PROJECT_MANAGER_NAME, START_DATE, END_DATE,
    PLANNED_START_DATE, PLANNED_END_DATE, PROGRESS, BUDGET, ACTUAL_COST,
    CREATOR, CREATION_DATE, MODIFIER, MODIFIED_DATE, DELETED,
    PROJECT_TYPE, DEPARTMENT, TAGS, DOCUMENT_PATH, ARCHIVED
) VALUES (
    'TEST_PROJECT_002',
    '文档管理系统开发',
    'DOC_SYSTEM_2024',
    '开发新的文档管理系统，支持多格式文档存储、检索和权限管理。',
    2, -- 已完成
    2, -- 中等优先级
    'admin',
    '系统管理员',
    '2024-01-01 09:00:00',
    '2024-03-31 18:00:00',
    '2024-01-01 09:00:00',
    '2024-03-31 18:00:00',
    100, -- 已完成100%
    300000.00, -- 预算30万
    280000.00, -- 实际花费28万
    'admin',
    NOW(),
    'admin',
    NOW(),
    0, -- 未删除
    'DEVELOPMENT', -- 开发项目
    '产品部',
    '文档管理,权限控制,检索系统',
    '/projects/doc_system_2024',
    0 -- 未归档
);

-- 插入项目成员数据（使用现有的PROJECT_MEMBER表）
INSERT INTO PROJECT_MEMBER (
    ID, PROJECT_ID, USER_ID, USERNAME, DISPLAY_NAME, EMAIL, PHONE, DEPARTMENT, POSITION,
    ROLE, ROLE_NAME, PERMISSION_LEVEL, JOIN_DATE, STATUS, WORKLOAD_PERCENT,
    CREATOR, CREATION_DATE, MODIFIER, MODIFIED_DATE, DELETED
) VALUES 
('MEMBER_001', 'TEST_PROJECT_001', 'admin', 'admin', '系统管理员', '<EMAIL>', '13800138001', '信息技术部', '项目经理', 1, '项目经理', 4, '2024-01-01', 1, 100, 'admin', NOW(), 'admin', NOW(), 0),
('MEMBER_002', 'TEST_PROJECT_001', 'developer1', 'developer1', '开发工程师1', '<EMAIL>', '13800138002', '信息技术部', '高级开发工程师', 2, '开发人员', 2, '2024-01-15', 1, 100, 'admin', NOW(), 'admin', NOW(), 0),
('MEMBER_003', 'TEST_PROJECT_001', 'developer2', 'developer2', '开发工程师2', '<EMAIL>', '13800138003', '信息技术部', '开发工程师', 2, '开发人员', 2, '2024-02-01', 1, 100, 'admin', NOW(), 'admin', NOW(), 0),
('MEMBER_004', 'TEST_PROJECT_001', 'tester1', 'tester1', '测试工程师', '<EMAIL>', '13800138004', '质量部', '测试工程师', 3, '测试人员', 2, '2024-05-01', 1, 80, 'admin', NOW(), 'admin', NOW(), 0);

-- 插入第二个项目的成员
INSERT INTO PROJECT_MEMBER (
    ID, PROJECT_ID, USER_ID, USERNAME, DISPLAY_NAME, EMAIL, PHONE, DEPARTMENT, POSITION,
    ROLE, ROLE_NAME, PERMISSION_LEVEL, JOIN_DATE, STATUS, WORKLOAD_PERCENT,
    CREATOR, CREATION_DATE, MODIFIER, MODIFIED_DATE, DELETED
) VALUES 
('MEMBER_005', 'TEST_PROJECT_002', 'admin', 'admin', '系统管理员', '<EMAIL>', '13800138001', '产品部', '项目经理', 1, '项目经理', 4, '2024-01-01', 1, 100, 'admin', NOW(), 'admin', NOW(), 0),
('MEMBER_006', 'TEST_PROJECT_002', 'developer3', 'developer3', '前端开发工程师', '<EMAIL>', '13800138005', '产品部', '前端工程师', 2, '开发人员', 2, '2024-01-01', 1, 100, 'admin', NOW(), 'admin', NOW(), 0);

-- 提交事务
COMMIT; 