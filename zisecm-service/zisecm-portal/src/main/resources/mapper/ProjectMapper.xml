<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ecm.portal.mapper.ProjectMapper">

    <!-- 插入项目 -->
    <insert id="insertProject" parameterType="com.ecm.portal.entity.ProjectEntity">
        INSERT INTO PROJECT (
            ID, PROJECT_NAME, PROJECT_CODE, DESCRIPTION, PROJECT_MANAGER, PROJECT_MANAGER_NAME,
            START_DATE, END_DATE, PLANNED_START_DATE, PLANNED_END_DATE, BUDGET, ACTUAL_COST, STATUS, PRIORITY, PROGRESS,
            PROJECT_TYPE, DEPARTMENT, TAGS, DOCUMENT_PATH,
            CREATOR, CREATION_DATE, MODIFIER, MODIFIED_DATE, DELETED
        ) VALUES (
            #{id}, #{projectName}, #{projectCode}, #{description}, #{projectManager}, #{projectManagerName},
            #{startDate}, #{endDate}, #{plannedStartDate}, #{plannedEndDate}, #{budget}, #{actualCost}, #{status}, #{priority}, #{progress},
            #{projectType}, #{department}, #{tags}, #{documentPath},
            #{creator}, #{creationDate}, #{modifier}, #{modifiedDate}, #{deleted}
        )
    </insert>

    <!-- 更新项目 -->
    <update id="updateProject" parameterType="com.ecm.portal.entity.ProjectEntity">
        UPDATE PROJECT SET
        <if test="projectName != null">PROJECT_NAME = #{projectName},</if>
        <if test="projectCode != null">PROJECT_CODE = #{projectCode},</if>
        <if test="description != null">DESCRIPTION = #{description},</if>
        <if test="projectManager != null">PROJECT_MANAGER = #{projectManager},</if>
        <if test="projectManagerName != null">PROJECT_MANAGER_NAME = #{projectManagerName},</if>
        <if test="startDate != null">START_DATE = #{startDate},</if>
        <if test="endDate != null">END_DATE = #{endDate},</if>
        <if test="plannedStartDate != null">PLANNED_START_DATE = #{plannedStartDate},</if>
        <if test="plannedEndDate != null">PLANNED_END_DATE = #{plannedEndDate},</if>
        <if test="budget != null">BUDGET = #{budget},</if>
        <if test="actualCost != null">ACTUAL_COST = #{actualCost},</if>
        <if test="status != null">STATUS = #{status},</if>
        <if test="priority != null">PRIORITY = #{priority},</if>
        <if test="progress != null">PROGRESS = #{progress},</if>
        <if test="projectType != null">PROJECT_TYPE = #{projectType},</if>
        <if test="department != null">DEPARTMENT = #{department},</if>
        <if test="tags != null">TAGS = #{tags},</if>
        <if test="documentPath != null">DOCUMENT_PATH = #{documentPath},</if>
        MODIFIER = #{modifier},
        MODIFIED_DATE = #{modifiedDate}
        WHERE ID = #{id} AND DELETED = 0
    </update>

    <!-- 软删除项目 -->
    <update id="deleteProject">
        UPDATE PROJECT SET DELETED = 1, MODIFIED_DATE = NOW() WHERE ID = #{projectId}
    </update>

    <!-- 根据ID查询项目 -->
    <select id="selectProjectById" resultType="java.util.Map">
        SELECT * FROM PROJECT WHERE ID = #{projectId} AND DELETED = 0
    </select>

    <!-- 查询项目列表 -->
    <select id="selectProjectList" resultType="java.util.Map">
        SELECT * FROM PROJECT
        WHERE DELETED = 0
        <if test="conditions.projectName != null and conditions.projectName != ''">
            AND PROJECT_NAME LIKE CONCAT('%', #{conditions.projectName}, '%')
        </if>
        <if test="conditions.status != null">
            AND STATUS = #{conditions.status}
        </if>
        <if test="conditions.projectManager != null and conditions.projectManager != ''">
            AND PROJECT_MANAGER LIKE CONCAT('%', #{conditions.projectManager}, '%')
        </if>
        <if test="conditions.startDate != null">
            AND START_DATE &gt;= #{conditions.startDate}
        </if>
        <if test="conditions.endDate != null">
            AND END_DATE &lt;= #{conditions.endDate}
        </if>
        ORDER BY CREATION_DATE DESC
    </select>

    <!-- 统计项目数量 -->
    <select id="countProjects" resultType="int">
        SELECT COUNT(*) FROM PROJECT
        WHERE DELETED = 0
        <if test="conditions.projectName != null and conditions.projectName != ''">
            AND PROJECT_NAME LIKE CONCAT('%', #{conditions.projectName}, '%')
        </if>
        <if test="conditions.status != null">
            AND STATUS = #{conditions.status}
        </if>
        <if test="conditions.projectManager != null and conditions.projectManager != ''">
            AND PROJECT_MANAGER LIKE CONCAT('%', #{conditions.projectManager}, '%')
        </if>
        <if test="conditions.startDate != null">
            AND START_DATE &gt;= #{conditions.startDate}
        </if>
        <if test="conditions.endDate != null">
            AND END_DATE &lt;= #{conditions.endDate}
        </if>
    </select>

    <!-- 搜索项目 -->
    <select id="searchProjects" resultType="java.util.Map">
        SELECT * FROM PROJECT
        WHERE DELETED = 0
        <if test="keyword != null and keyword != ''">
            AND (PROJECT_NAME LIKE CONCAT('%', #{keyword}, '%')
            OR PROJECT_CODE LIKE CONCAT('%', #{keyword}, '%')
            OR DESCRIPTION LIKE CONCAT('%', #{keyword}, '%')
            OR PROJECT_MANAGER LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        ORDER BY CREATION_DATE DESC
    </select>

    <!-- 根据状态查询项目 -->
    <select id="selectProjectsByStatus" resultType="java.util.Map">
        SELECT * FROM PROJECT
        WHERE DELETED = 0
        <if test="status != null">
            AND STATUS = #{status}
        </if>
        ORDER BY CREATION_DATE DESC
    </select>

    <!-- 根据项目经理查询项目 -->
    <select id="selectProjectsByManager" resultType="java.util.Map">
        SELECT * FROM PROJECT
        WHERE DELETED = 0
        <if test="managerId != null and managerId != ''">
            AND PROJECT_MANAGER = #{managerId}
        </if>
        ORDER BY CREATION_DATE DESC
    </select>

    <!-- 更新项目状态 -->
    <update id="updateProjectStatus">
        UPDATE PROJECT SET STATUS = #{status}, MODIFIER = #{userId}, MODIFIED_DATE = NOW()
        WHERE ID = #{projectId} AND DELETED = 0
    </update>

    <!-- 更新项目进度 -->
    <update id="updateProjectProgress">
        UPDATE PROJECT SET PROGRESS = #{progress}, MODIFIER = #{userId}, MODIFIED_DATE = NOW()
        WHERE ID = #{projectId} AND DELETED = 0
    </update>

    <!-- 检查项目名称是否存在 -->
    <select id="checkProjectNameExists" resultType="int">
        SELECT COUNT(*) FROM PROJECT
        WHERE PROJECT_NAME = #{projectName} AND DELETED = 0
        <if test="excludeId != null and excludeId != ''">
            AND ID != #{excludeId}
        </if>
    </select>

    <!-- 检查项目代码是否存在 -->
    <select id="checkProjectCodeExists" resultType="int">
        SELECT COUNT(*) FROM PROJECT
        WHERE PROJECT_CODE = #{projectCode} AND DELETED = 0
        <if test="excludeId != null and excludeId != ''">
            AND ID != #{excludeId}
        </if>
    </select>

    <!-- 获取项目统计信息 -->
    <select id="getProjectStatistics" resultType="java.util.Map">
        SELECT
        COUNT(*) as totalProjects,
        SUM(CASE WHEN STATUS = 0 THEN 1 ELSE 0 END) as planningProjects,
        SUM(CASE WHEN STATUS = 1 THEN 1 ELSE 0 END) as activeProjects,
        SUM(CASE WHEN STATUS = 2 THEN 1 ELSE 0 END) as pausedProjects,
        SUM(CASE WHEN STATUS = 3 THEN 1 ELSE 0 END) as completedProjects,
        SUM(CASE WHEN STATUS = 4 THEN 1 ELSE 0 END) as cancelledProjects,
        AVG(PROGRESS) as avgProgress,
        SUM(BUDGET) as totalBudget
        FROM PROJECT WHERE DELETED = 0
    </select>

    <!-- 获取用户参与的项目 -->
    <select id="selectUserProjects" resultType="java.util.Map">
        SELECT DISTINCT p.* FROM PROJECT p
        LEFT JOIN PROJECT_MEMBER pm ON p.ID = pm.PROJECT_ID
        WHERE p.DELETED = 0 AND pm.DELETED = 0
        <if test="userId != null and userId != ''">
            AND (p.PROJECT_MANAGER = #{userId} OR pm.USER_ID = #{userId})
        </if>
        ORDER BY p.CREATION_DATE DESC
    </select>

    <!-- 批量更新项目状态 -->
    <update id="batchUpdateProjectStatus">
        UPDATE PROJECT SET STATUS = #{status}, MODIFIER = #{userId}, MODIFIED_DATE = NOW()
        WHERE DELETED = 0 AND ID IN
        <foreach collection="projectIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量删除项目 -->
    <update id="batchDeleteProjects">
        UPDATE PROJECT SET DELETED = 1, MODIFIER = #{userId}, MODIFIED_DATE = NOW()
        WHERE DELETED = 0 AND ID IN
        <foreach collection="projectIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 归档项目 -->
    <update id="archiveProject">
        UPDATE PROJECT SET STATUS = 5, MODIFIER = #{userId}, MODIFIED_DATE = NOW()
        WHERE ID = #{projectId} AND DELETED = 0
    </update>

</mapper>
