server.port=6090

jasypt.encryptor.password = dnmc@2022

#spring.datasource.url=*******************************************
#spring.datasource.username=<PERSON><PERSON>(twMbYt/QMq9ME8qxVZ8JIw==)
#spring.datasource.password=ENC(1IlewAZnjIftb81eHk5TAcRljDwXa4RG)
##Orcale9????oracle.jdbc.driver.OracleDriver??oracle.jdbc.OracleDriver
#spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
#spring.datasource.validationQuery=SELECT 1 from dual
#npdcmsdev2?????
#npdcmsdevgx2:????
#npdcmsdevzj2:????
#npdcmsdevhn2:??????

spring.datasource.url=********************************************************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=DGSQ@df51!Zg147258
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.validationQuery=SELECT 1 from dual

#
#spring.datasource.url=******************************************************************************************************************************************************
#spring.datasource.username=root
#spring.datasource.password=root
#spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.datasource.validationQuery=SELECT 1 from dual


swagger.basePackage = com.ecm.flowable.controller
flowable.check-process-definitions=false
rocketmq.namesrvaddr=**************:9876
spring.application.name=ecm

# JavaMailSender \u90AE\u4EF6\u53D1\u9001\u7684\u914D\u7F6E
spring.mail.host=cgn-internal-mail.gnpjvc.cgnpc.com.cn
spring.mail.username=PDNMCKM
spring.mail.password=9y18r-GCR-XGMM
spring.mail.from=<EMAIL>
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.ssl.trust=cgn-internal-mail.gnpjvc.cgnpc.com.cn
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true

#\u7F13\u5B58\u6539\u7528redis\u7F13\u5B58
spring.cache.type=redis
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.password=
spring.redis.database=0

#pageoffice\u7684\u8D44\u6E90\u76EE\u5F55\uFF0C\u53EF\u81EA\u5DF1\u914D\u7F6E\u4EFB\u610F\u76EE\u5F55
########################################################
###PageOffice
########################################################
posyspath=D:/pageoffice

#\u7248\u672C\u5E93\u6D4B\u8BD5
#ljjun,\u5BF9\u65E5\u5FD7\u8FDB\u884C\u5904\u7406\uFF0C\u5BF9\u6253\u5370\u4FE1\u606F\u8FDB\u884C\u9650\u5236\uFF0C\u592A\u591A\u6253\u5370\u4F1A\u4E25\u91CD\u5F71\u54CD\u542F\u52A8\u901F\u5EA6
logging.pattern.console=%d{yyyy-MM-dd'T'HH:mm:ssXXX} [%thread] %-5level - %logger - %msg%n
logging.pattern.file=%d{yyyy-MM-dd'T'HH:mm:ssXXX} [%thread] %-5level - %logger - %msg%n
logging.level.root=INFO
#\u751F\u4EA7\u73AF\u5883\u4E0B\u6B64\u5904\u6539\u4E3AINFO\uFF0C\u51CF\u5C11\u65E5\u5FD7\u6253\u5370\uFF0C\u5F00\u53D1\u4F7F\u7528debug\u65B9\u4FBF\u5B9A\u4F4D
logging.level.com.ecm=info
logging.file.name=c:/NuclearPower/NuclearPower.log
logging.logback.rollingpolicy.max-history=180
logging.logback.rollingpolicy.max-file-size=30MB
logging.charset.console=UTF-8
logging.charset.file=UTF-8


#\u6587\u6863\u5229\u7528\u5230\u671F\u5B9A\u65F6\u4EFB\u52A1\u63D0\u9192
#datasync.cron=0 0/5 * * * ? #\u4E94\u5206\u949F\u6267\u884C
#\u6BCF\u5929\u6267\u884C\u4E00\u6B21
datasync.cron=
#\u5230\u671F\u5929\u6570\u8BBE\u7F6E
expiryDays=

# \u516C\u53F8\u540D\u79F0
ecm.company.code=GuangXi
# \u5355\u70B9\u767B\u5F55 \u516C\u94A5
ecm.company.sso.key.public=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxDMpunXVsxqdzPcVzBjeos4yC1gE07RwSj4nxw7qgycs3iAncI9u6YMbrrUALVdP+I2gIxKx0lvXM3MnIg2ZQPkmnSedDmSVD2ZvbAYr+Z1rxu8Am2lTZvH4Rax49pFT/KTv0YzdoZgqyhX95k859mYh1gJzzMyg+HTK5rxkBxTXlsQ6TZdA1H8A76HSbWy/zAbnvQ2Od05Dhoj3sSuG7UcwYKNpY/l4oICJlZ+8WJa5EI+k1jCIubNBdFdwrXHY1LfMTtMeql7yWBQYmaQsav8b6xUsYuiXGNa8REF90i4okDr95Sh+/fI2g8tmSjzWd0DCV1OyUrTQh8dbVJFDmQIDAQAB
# \u5355\u70B9\u767B\u5F55 \u79C1\u94A5
ecm.company.sso.key.private=MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQDEMym6ddWzGp3M9xXMGN6izjILWATTtHBKPifHDuqDJyzeICdwj27pgxuutQAtV0/4jaAjErHSW9czcyciDZlA+SadJ50OZJUPZm9sBiv5nWvG7wCbaVNm8fhFrHj2kVP8pO/RjN2hmCrKFf3mTzn2ZiHWAnPMzKD4dMrmvGQHFNeWxDpNl0DUfwDvodJtbL/MBue9DY53TkOGiPexK4btRzBgo2lj+XiggImVn7xYlrkQj6TWMIi5s0F0V3CtcdjUt8xO0x6qXvJYFBiZpCxq/xvrFSxi6JcY1rxEQX3SLiiQOv3lKH798jaDy2ZKPNZ3QMJXU7JStNCHx1tUkUOZAgMBAAECggEBAJtbP/GLmNmne8hu5m2kS7Zyu6HL4paYTEodiXXP09ZiUN7aayBwEw0IagUfAbLlbkeMLX9YMEI3wuHUjuoiBnYyU2chlPVuo7F8FUJeaLfQ9SSi38Iv1oCvnT3eYjIrxWtKVzOA7YYlbcxzOYcCuQzotmY1rug+KEBUis3F4xbO/08zHQGqgXEYIPHEn8SZ7FK8aTIKHD5003GyzSbzQyyqXKRLX/4pnoCbxT9s/lxdGnlpOHBCCyE0yqFqiV4VqG59W5A8OYd9vU+RQETe8+njvSdEFCm8LII8aqdug5YnhhOywmxnqF/aljPzFFXC/3PemqfpC8zcapIsqPa+bfkCgYEA+HOBUuVejOrE0swSl58nFIvUqiKm2kFWrsZup0Ls5JI7kxzEVGMaV9JBPgCpEgD9OTQvUq1shTdRAQssBY46rd49EA/Ls39zcofe1fXVtuGDuEWOe6pagGTN3jY7c0yeMOvCUHJSFR7JcsAv9vAeO5EAd6nPm6lguYH/UoD1FdcCgYEAyik8+7A1SyAXrsGM1aGKPqtUld/X6Hg5QuMtGGlFICag0L56uxgq8nZyLSDx5qrux4HTPnLyn/3zBScrG9VkGyqLdVlnDbIwpeF7KeKCrCcMR7NLGdPBMdprcl2AHwpOuHj8lDwKN6ywRIkz0Wsm6pXAgtgKgglWBsonSaQbZA8CgYEAz0vIVnsMRbVIXb1psdpXyBvXZ9EbuDUV9yOXhbUgZKHiVR+GbAZ4H1hNmUcBygTLQyRYa7pJNmluC9nfdA80xIcOGc2kH3Myxxe4hHrmQv2sYl6ewG3lrwTshABp4AzIXHcJHQgVloumdzkFjBrSjs6wPieQEgfjrURa3OK+GucCgYAcW4aDY6dGH31S9TF1MhC7U7ssvtaBdIPLvyQvPW0F4oVCYsXCgtRbaIQBkpJ8IdQWxj5Ej0k7ahrxyRnB8L/3E5O2ZVEQWcN7MVLegDiGP3/4esz5FXwPKy1RQI5dTEvYkpgTjwx73MxpALn1EUvHMjRr5jQrI3Yew7KZINz6vwKBgQDNqqdnRLvYy8i8xudsJp/VUeNOiYI7fgaN2ey3g3zfz8W2jqc60bhfTiuvozKuv9ZNK/An6bKBYy8LZ8TBRvU2PU8wMB+LfjL8+TK3avVuxondbhL6oWV6aApMfWFqK2bkPaSfMYggcGpR3PsSENdSygIvrbIaoZoJNohlKv0VBA==
# \u5355\u70B9\u767B\u5F55AES
ecm.company.sso.iv=MIIBIjANBgkqhkiG
# \u5355\u70B9\u767B\u5F55AES key
ecm.company.sso.key=MIIEvwIBADANBgkq



# #########################################################################
server.servlet.context-path=/zisecm
server.servlet.encoding.force=true
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.tomcat.uri-encoding=UTF-8
spring.messages.encoding=UTF-8
spring.aop.proxy-target-class=true
# ######pathmatch.matching-strategy : Îªï¿½ï¿½springboot 2.7.xï¿½ï¿½ï¿½ï¿½urlï¿½ï¿½ï¿½ï¿½Â·ï¿½ï¿½ï¿½ÏµÄ¶ï¿½ï¿½Ð±ï¿½Üµï¿½ï¿½ï¿½ï¿½ï¿½/
# ######pathmatch.matching-strategy : ??springboot 2.7.x??url?????????????/
spring.mvc.pathmatch.matching-strategy=ant_path_matcher

# session time out
com.ecm.session.timeout=14400

#thymeleaf start
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.check-template-location=true
spring.thymeleaf.template-resolver-order=1
spring.thymeleaf.suffix=.html
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.content-type=text/html
#\u65B0\u7248\u672C\u4E2DHTML5\u5DF2\u7ECF\u5E9F\u5F03\uFF0C\u4F7F\u7528HTML\u5373\u53EF
spring.thymeleaf.mode=HTML

jwt.secret=secret
jwt.expiration=7200000
jwt.token=Authorization

#\u6307\u5B9A\u6620\u5C04\u6587\u4EF6
mybatis.mapperLocations=classpath*:mapper/*.xml
mybatis.configuration.call-setters-on-nulls=true
#mybatis.configuration.database-id=dm
mybatis.global-config.bannerbanner = false

web.filter.pattern=/*

spring.servlet.multipart.enabled = true
spring.servlet.multipart.max-file-size = 4000MB
spring.servlet.multipart.max-request-size=10000MB

spring.jackson.time-zone=GMT+8
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss

# flowable.eventregistry.enabled=false
# flowable.eventregistry.enable-change-detection=false
flowable.eventregistry.change-detection-delay=10000000000000000
flowable.database-schema-update = false
com.ecm.cache.name.prefix=zisecmgx.
com.ecm.cache.type = java

property.config.httpTop=https://elinkuat.spic.com.cn/cgi-bin
property.config.httpCode=https://open.weixin.qq.com/connect/oauth2/authorize
property.config.httpRedirectUri=https://ndcms.elinkpoc.spic.com.cn/zisecm/redirectCode
property.config.agentId=1000568
property.config.secret=TCn9H95AxbuHiC7OovDhgMV_idccOXqcjCZLVPgvljg
property.config.corpID=wwa1efd2629060592a
property.config.api.getToken=/gettoken
property.config.api.sendMsg=/message/send
property.config.api.userInfo=/user/getuserinfo
property.config.vue.url=https://ndcms.elinkpoc.spic.com.cn/#/pages/workflow/task/todolist
property.config.vue.error=https://ndcms.elinkpoc.spic.com.cn/#/pages/error/error
property.config.messageUrl=https://www.baidu.com


# Test
# spic.sso.url=http://10.80.59.158/idp/oauth2/
# spic.sso.client.secret=5d57726d984c449da9def84857a41ae9
# pro 10.80.128.234
spic.sso.client.id=hdwjxt
spic.sso.url=http://iam.spic/idp/oauth2/
spic.sso.client.secret=7f10638924e144b5b79d7feddda3b7b3
