package com.zisecm.jobs.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

@Service
public class EncryptSyncService {

    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;

    @Value("${encrypt.system.url}")
    private String encryptUrl;

    public boolean syncData(String type,Object jData){
        String url = encryptUrl + "/ProductsResource/edms/dataSync";
        logger.debug(url);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        JSONObject params = new JSONObject();
        params.put("authType", type);
        params.put("jdata", jData);

        HttpEntity<String> request = new HttpEntity<>(params.toJSONString(),headers);
        logger.debug("request:"+params.toJSONString());
        restTemplate.getMessageConverters().set(1,new StringHttpMessageConverter(StandardCharsets.UTF_8));
        ResponseEntity<JSONObject> result = restTemplate.postForEntity(url, request, JSONObject.class);

        if (result.getStatusCode() == HttpStatus.OK && result.getBody().getString("data") !=null) {
            logger.debug("data:"+result.getBody().getString("data"));
            JSONArray jslist = result.getBody().getJSONArray("data");
            if (jslist != null && jslist.size() > 0) {
                LinkedHashMap obj = (LinkedHashMap)jslist.get(0);
                if(obj.get("code")!=null && obj.get("code").equals("0")){
                    return true;
                }
            }
        }else{
            logger.error(result.getBody().toJSONString());
        }
        return false;
    }
}
