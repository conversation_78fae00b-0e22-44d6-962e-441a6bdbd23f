package com.zisecm.jobs.utils;

import com.ecm.core.cache.manager.CacheManagerOper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "ecm")
public class EcmConfig {
    @Autowired
    protected Environment env;
    /**
     * API RUL BASE
     */
//    private String url = "http://localhost:6090/zisecm";

    private String url = null;
    /**
     * 登录名
     */
    private String username = null;
    /**
     * 登录密码
     */
    private String password = null;
    /**
     * 获取文件路径
     */
    private String getPath = "./downloads";
    /**
     * 处理数据后是否删除数据
     */
    private boolean doneDelete = false;

    /**
     * 处理时间间隔 1000 为1秒
     */
    private long fixedDelay = 5000;

    /**
     * 查询处理数据量
     */
    private int pageSize = 10;
    private String transEventType = "ecm_to_pdf";

    public String getUrl() {
        return env.getProperty("ecm.service.path");
    }

    public long getFixedDelay() {
        return fixedDelay;
    }

    public void setFixedDelay(long fixedDelay) {
        this.fixedDelay = fixedDelay;
    }

    public boolean isDoneDelete() {
        return doneDelete;
    }

    public void setDoneDelete(boolean doneDelete) {
        this.doneDelete = doneDelete;
    }

    public String getTransEventType() {
        return transEventType;
    }

    public void setTransEventType(String transEventType) {
        this.transEventType = transEventType;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getGetPath() {
        return getPath;
    }

    public void setGetPath(String getPath) {
        this.getPath = getPath;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUsername() {
        return 	CacheManagerOper.getEcmParameter("workflowUserName").getValue();
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return  CacheManagerOper.getEcmParameter("workflowUserPassword").getValue();
    }

    public void setPassword(String password) {
        this.password = password;
    }

}
