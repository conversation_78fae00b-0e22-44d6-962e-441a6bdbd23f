package com.zisecm.jobs.service.crossorg;

import com.alibaba.fastjson2.JSONObject;
import com.ecm.core.entity.EcmContent;
import com.ecm.core.entity.EcmDocument;
import com.ecm.core.service.ContentService;
import com.ecm.core.service.DocumentService;
import com.ecm.core.service.RelationService;
import com.zisecm.jobs.utils.HttpClientUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;

@Service
public class SDSendSHService {
    @Autowired
    private DocumentService documentService;
    @Autowired
    private ContentService contentService;
    @Autowired
    private RelationService relationService;

    public void sendOrderData(String token,String userName,String password,String id,String jsonMapName,String processInstenceId) throws Exception {

        EcmDocument form= documentService.getObjectById(token,id);
        EcmContent content= contentService.getPrimaryContent(token,id);
        InputStream in= contentService.getContentStream(token,content);
        String remoteToken= HttpClientUtils.login("SHBaseurl",userName,password);

        EcmDocument newForm=new EcmDocument();
        newForm.addAttribute("ID",form.getAttributeValue("SYN_ID"));
        newForm.addAttribute("C_STRING1",processInstenceId);
        String dataStr= HttpClientUtils.executeRestWithFile(remoteToken,"SHBaseurl","/dc/createOrUpdateDoc", "metaData",
                JSONObject.toJSONString(newForm.getAttributes()),"uploadFile",null,"attachFiles",null);
        JSONObject data= JSONObject.parseObject(dataStr);
//        //编写将remoteID写入syn_id
//        form.addAttribute("SYN_ID",data.get("formId"));
//        documentService.updateObject(token,form);
        documentService.newAudit(token, "workflow", "syncDoc", id, data.get("id").toString(), "发送签名传递单至电站");
    }
}
