package com.zisecm.jobs.service;

import com.alibaba.fastjson.JSONObject;

import java.io.File;
import java.util.List;
import java.util.Map;

public interface EcmClientService {

    /**
     * 获取token
     * 
     * @return
     */
    public String getToken();

    /**
     * 释放token
     * 
     * @param token
     */
    public void releaseToken(String token);

    /**
     * 下载文件
     * 
     * @param docId 文档ID
     * @param token
     */
    public File download(String docId, String fileName, String token,boolean needSubfolder);

    public void uploadRendition(String token, String docId, String trancFile);

    public List<JSONObject> getQueueItems(String token, String eventName, boolean hasContent, int pageSize,
            int pageIndex);

    public void delQueueItem(String token, String id);

    public JSONObject getDocumentInfoById(String token, String id);

    public void saveDocument(String token, Map<String,Object> attrs);

    public List<JSONObject> queryDocument(String token,String gridName, String condition, int pageSize,
                                    int pageIndex);
    public void startUpWorkflow(String token,String processConfigName,String formId);
}
