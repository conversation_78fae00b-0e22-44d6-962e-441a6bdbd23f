package com.zisecm.jobs.service;

import com.ecm.common.util.DateUtils;
import com.ecm.core.dao.EcmDocumentMapper;
import com.ecm.core.service.DocumentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
@Component
public class UpdateSiteDocSchedule {
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    private EcmDocumentMapper ecmDocumentMapper;

    @Scheduled(cron = "${corn.TimerTaskJob.updateSiteDocInfo}")
    public void runJob(){
        logger.debug("Start UpdateSiteDocSchedule.");
        Calendar dt = Calendar.getInstance(TimeZone.getDefault());
        dt.add(Calendar.DAY_OF_YEAR,-1);
        String date = DateUtils.DateToStr(dt.getTime(),"yyyy-MM-dd");
        String sql="select a.ID,a.TITLE,a.STATUS,a.CODING,a.REVISION,a.C_DOC_STATUS,b.ID as ITEMID,a.C_PAGE_COUNT,a.C_IS_BENCHMARK_DOC from ecm_document a,ecm_document b where b.TYPE_NAME ='工作文件清单' and b.PARENT_ID =a.ID and a.MODIFIED_DATE is not null and a.MODIFIED_DATE >'"+date+"'";
        List<Map<String,Object>> list = ecmDocumentMapper.executeSQL(sql);
        if(list!=null){
            logger.debug("doc size:"+list.size());
            for(Map<String,Object> map: list){
                String usql = "update ecm_document set ";
                usql += " TITLE='"+ map.get("TITLE").toString().replace("'","''")+"'";
                usql += " ,C_DOC_STATUS='"+ map.get("C_DOC_STATUS")+"'";
                usql += " ,C_ITEM_STATUS1='"+ map.get("STATUS")+"'";
                usql += " ,C_PAGE_COUNT="+ map.get("C_PAGE_COUNT");
                //usql += " ,REVISION='"+ map.get("REVISION")+"'";
                usql += " where ID='"+map.get("ITEMID")+"'";
                ecmDocumentMapper.executeSQL(usql);
            }
        }
        logger.debug("end UpdateSiteDocSchedule.");
    }
}
