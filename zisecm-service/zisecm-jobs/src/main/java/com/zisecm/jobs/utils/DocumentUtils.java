package com.zisecm.jobs.utils;

import com.ecm.core.entity.EcmDocument;
import com.ecm.core.exception.AccessDeniedException;
import com.ecm.core.exception.EcmException;
import com.ecm.core.service.DocumentService;

import java.util.List;
import java.util.Map;

public class DocumentUtils {


    public static EcmDocument getCurrentDocument(String token, DocumentService documentService, EcmDocument doc) throws EcmException {
        String typeName = doc.getTypeName();
        String coding = doc.getCoding();
        String sql = "TYPE_NAME='"+typeName+"' AND CODING='"+coding+"' AND IS_CURRENT=1 AND ID<>'"+doc.getId()+"'";
        List<EcmDocument> list = documentService.getObjectsAllColumnByTypeName(token,typeName,sql);
        if(list.size()>0){
            return list.get(0);
        }
        return null;
    }


    /**
     * 获取上游文件编码
     * @param token
     * @param docService
     * @param docId 下游文件ID
     * @return
     */
    public  static String getParentCoding(String token, DocumentService docService,String docId){
        String retStr = "";
//        String sql = "select a.CODING from ECM_DOCUMENT a, ECM_RELATION b where b.NAME='上游' AND a.ID=b.PARENT_ID AND b.CHILD_ID='"+docId+"'";
        String sql = "select a.CODING from ECM_DOCUMENT a, ECM_RELATION b where a.IS_CURRENT=1 AND b.NAME='上游' AND a.ID=b.CHILD_ID AND b.PARENT_ID='"+docId+"'";
        try {
            List<Map<String,Object>> list = docService.getMapList(token,sql);
            for(Map<String,Object> map: list){
                if(retStr.length()==0){
                    retStr = (String)map.get("CODING");
                }else{
                    retStr +=   ";"+(String)map.get("CODING");
                }
            }
        } catch (EcmException e) {
            e.printStackTrace();
        }
        return retStr;
    }

}
