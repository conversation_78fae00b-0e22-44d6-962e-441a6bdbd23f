package com.zisecm.jobs.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zisecm.jobs.service.EcmClientService;
import com.zisecm.jobs.utils.EcmConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;

@Service
public class EcmClientServiceImpl implements EcmClientService {

    @Autowired
    private EcmConfig ecmConfig;

    @Resource
    private RestTemplate restTemplate;

    private Logger logger = LoggerFactory.getLogger(EcmClientServiceImpl.class);

    @Override
    public String getToken() {
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
        params.add("username", ecmConfig.getUsername());
        params.add("password", ecmConfig.getPassword());
        String url = ecmConfig.getUrl() + "/userLogin";
        logger.debug(url);
        ResponseEntity<JSONObject> resultResponseEntity = this.request(url, params);
        if (resultResponseEntity.getStatusCode() == HttpStatus.OK) {
            if (resultResponseEntity.getBody().getIntValue("code") == 200) {
                return resultResponseEntity.getBody().getJSONObject("data").getString("token");
            } else {
                return null;
            }

        } else {
            return null;
        }
    }

    @Override
    public void releaseToken(String token) {
        String url = ecmConfig.getUrl() + "/userLogout";
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
        ResponseEntity<JSONObject> resultResponseEntity = this.request(url, params, MediaType.APPLICATION_JSON, token);
        if (resultResponseEntity.getStatusCode() == HttpStatus.OK) {
            logger.debug(resultResponseEntity.getBody().toJSONString());

        }
    }

    private ResponseEntity<JSONObject> request(String url, MultiValueMap<String, Object> params) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params,
                headers);
        ResponseEntity<JSONObject> result = this.restTemplate.postForEntity(url, request, JSONObject.class);
        return result;
    }

    private ResponseEntity<JSONObject> request(String url, MultiValueMap<String, Object> params, MediaType mediaType,
            String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(mediaType);
        headers.set("token", token);
        HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params,
                headers);
        ResponseEntity<JSONObject> result = this.restTemplate.postForEntity(url, request, JSONObject.class);
        return result;
    }

    @Override
    public File download(String docId, String fileName, String token,boolean needSubfolder) {
        String url = ecmConfig.getUrl() + "/dc/getContent?id=" + docId + "&token=" + token;
        String filePath = ecmConfig.getGetPath() + "/";
        if(needSubfolder){
            filePath += docId + "/";
        }

        try {
            Files.createDirectories(Paths.get(filePath));
        } catch (IOException e) {
            e.printStackTrace();
        }
        String fileFullPath = filePath + fileName;
        RequestCallback requestCallback = request -> request.getHeaders()
                .setAccept(Arrays.asList(MediaType.APPLICATION_OCTET_STREAM, MediaType.ALL));
        new RestTemplate().execute(url, HttpMethod.GET, requestCallback, clientHttpResponse -> {
            Files.copy(clientHttpResponse.getBody(), Paths.get(fileFullPath), StandardCopyOption.REPLACE_EXISTING);
            return null;
        });
        File file = new File(fileFullPath);
        return file;
    }

    @Override
    public void uploadRendition(String token, String docId, String trancFile) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("token", token);
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
        JSONObject metaData = new JSONObject();
        metaData.put("ID", docId);
        params.add("metaData", metaData.toJSONString());
        FileSystemResource resource = new FileSystemResource(new File(trancFile));
        params.add("uploadFile", resource);
        HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params,
                headers);
        String url = ecmConfig.getUrl() + "/dc/addRendition";
        ResponseEntity<JSONObject> result = this.restTemplate.postForEntity(url, request, JSONObject.class);
        if (result.getStatusCode() == HttpStatus.OK) {
            logger.debug(result.getBody().toJSONString());
        }
    }

    @Override
    public List<JSONObject> getQueueItems(String token, String eventName, boolean hasContent, int pageSize,
            int pageIndex) {

        logger.debug(token);
        logger.debug(eventName);
        logger.debug(pageIndex + "");
        logger.debug(pageSize + "");
        String url = ecmConfig.getUrl() + "/queue/getQueueItems";
        logger.debug(url);

        JSONObject params = new JSONObject();
        params.put("pageIndex", String.valueOf(pageIndex));
        params.put("pageSize", String.valueOf(pageSize));
        StringBuffer sb = new StringBuffer("EVENT_NAME='" + eventName + "'");
        if (hasContent) {
            // sb.append(
            // " AND OBJECT_ID in (SELECT ID FROM ECM_DOCUMENT WHERE FORMAT_NAME IN
            // ('doc','docx','xls','xlsx','ppt','pptx')) ");
        }
        params.put("condition", sb.toString());
        MediaType type = MediaType.parseMediaType("application/json;charset=UTF-8");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(type);
        headers.set("token", token);
        
        HttpEntity<String> request = new HttpEntity<>(params.toJSONString(), headers);
        
        this.restTemplate.getMessageConverters().set(1,new StringHttpMessageConverter(StandardCharsets.UTF_8));
        
        
        ResponseEntity<JSONObject> result = this.restTemplate.postForEntity(url, request, JSONObject.class);

        List<JSONObject> resultlist = new ArrayList<>();
        if (result.getStatusCode() == HttpStatus.OK && result.getBody().getIntValue("code") == 200) {
            JSONArray jslist = result.getBody().getJSONArray("data");
            if (jslist != null && jslist.size() > 0) {
                for (int i = 0; i < jslist.size(); i++) {
                    resultlist.add(jslist.getJSONObject(i));
                }
            }
        }
        return resultlist;

    }

    @Override
    public void delQueueItem(String token, String id) {
        String url = ecmConfig.getUrl() + "/queue/deleteQueueItem";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("token", token);
        JSONObject params = new JSONObject();
        params.put("id", id);
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(params.getInnerMap(), headers);
        ResponseEntity<JSONObject> result = this.restTemplate.postForEntity(url, request, JSONObject.class);
        logger.debug(result.getStatusCode().value() + "");
    }

    @Override
    public JSONObject getDocumentInfoById(String token, String id) {
        String url = ecmConfig.getUrl() + "/dc/getDocument";
        HttpHeaders headers = new HttpHeaders();
        // headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("token", token);

        MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
        params.add("id", id);
        HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(params, headers);
        ResponseEntity<JSONObject> result = this.restTemplate.postForEntity(url, request, JSONObject.class);
        if (result.getStatusCode() == HttpStatus.OK && result.getBody().containsKey("data")) {
            return result.getBody().getJSONObject("data").getJSONObject("data");

        } else {

            return null;
        }
    }

    @Override
    public void saveDocument(String token, Map<String, Object> attrs) {

        String url = ecmConfig.getUrl() + "/dc/saveDocument";
        HttpHeaders headers = new HttpHeaders();
        // headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("token", token);

        JSONObject params = new JSONObject();
        params.put("data", attrs);
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(params.getInnerMap(), headers);
        ResponseEntity<JSONObject> result = this.restTemplate.postForEntity(url, request, JSONObject.class);
        if (result.getStatusCode() == HttpStatus.OK) {
             logger.debug(result.getBody().toString());
        }else{
            logger.error("saveDocument:"+result.getStatusCode());
        }
    }

    @Override
    public List<JSONObject> queryDocument(String token, String gridName, String condition, int pageSize, int pageIndex) {
        String url = ecmConfig.getUrl() + "/dc/getDocuments";
        logger.debug(url);

        JSONObject params = new JSONObject();
        params.put("pageIndex", String.valueOf(pageIndex));
        params.put("pageSize", String.valueOf(pageSize));
        params.put("condition", condition);
        params.put("gridName", gridName);
        params.put("folderId", "");
        params.put("orderBy", "");
        MediaType type = MediaType.parseMediaType("application/json;charset=UTF-8");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(type);
        headers.set("token", token);

        HttpEntity<String> request = new HttpEntity<>(params.toJSONString(), headers);

        this.restTemplate.getMessageConverters().set(1,new StringHttpMessageConverter(StandardCharsets.UTF_8));


        ResponseEntity<JSONObject> result = this.restTemplate.postForEntity(url, request, JSONObject.class);

        List<JSONObject> resultlist = new ArrayList<>();
        if (result.getStatusCode() == HttpStatus.OK && result.getBody().getIntValue("code") == 200) {
            JSONArray jslist = result.getBody().getJSONObject("data").getJSONArray("data");
            if (jslist != null && jslist.size() > 0) {
                for (int i = 0; i < jslist.size(); i++) {
                    resultlist.add(jslist.getJSONObject(i));
                }
            }
        }
        return resultlist;
    }

    @Override
    public void startUpWorkflow(String token,String processConfigName, String formId) {

        String url=ecmConfig.getUrl()+"/workflow/startWorkflow";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("token", token);
        Map<String, Object> params = new HashMap<>();
        params.put("formId", formId);
        params.put("processConfigName", processConfigName);
        HttpEntity<String> request = new HttpEntity<String>(JSONObject.toJSONString(params),
                headers);
        ResponseEntity<JSONObject> result = this.restTemplate.postForEntity(url, request, JSONObject.class);

    }

}
