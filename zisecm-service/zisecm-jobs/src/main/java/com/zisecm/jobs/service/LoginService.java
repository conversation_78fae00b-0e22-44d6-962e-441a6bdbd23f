package com.zisecm.jobs.service;

import com.ecm.core.ActionContext;
import com.ecm.core.cache.manager.CacheManagerOper;
import com.ecm.core.exception.EcmException;
import com.ecm.core.search.ESClient;
import com.ecm.core.service.AuthService;
import com.ecm.icore.service.IEcmSession;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
public class LoginService {

	private final Logger logger = LoggerFactory.getLogger(LoginService.class);

	@Autowired
	private AuthService authService;

	public String login(String token){

		if (token == null || authService.login(token) == null) {
			String user = null;
			try {
				user = CacheManagerOper.getEcmParameter("workflowUserName").getValue();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				// e.printStackTrace();
				return null;
			}

			String password = CacheManagerOper.getEcmParameter("workflowUserPassword").getValue();
			
			if(CacheManagerOper.getEcmAttributes("ECM_DOCUMENT")==null) {
				return null;
			}
			
			try {
				token = authService.login("ECMJobs", user, password).getToken();
				
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
				return null;
			}
		}else {
			try {
				return authService.login(token).getToken();
			}catch (Exception ex){
				// 登录超时，重新登录
				return login(null);
			}
		}
		return token;
	}

	public IEcmSession   getEcmSession(){
		IEcmSession session = null;
		String user = null;
		try {
			user = CacheManagerOper.getEcmParameter("workflowUserName").getValue();
			String password = CacheManagerOper.getEcmParameter("workflowUserPassword").getValue();
			session = authService.login("ECMJobs", user, password);
		} catch (Exception e) {
			 e.printStackTrace();
			return null;
		}
		return session;
	}

	public void logout(String token){
		authService.logout(token);
	}
}
