package com.zisecm.jobs.service.mail;

import com.ecm.core.cache.manager.CacheManagerOper;
import com.ecm.core.service.MailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring5.SpringTemplateEngine;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ServiceDocMail {
	private static final Logger logger = LoggerFactory.getLogger(ServiceDocMail.class);
	@Autowired
    private MailService mailService;

	@Autowired
	private SpringTemplateEngine templateEngine;

	@Autowired
	private Environment env;
	
	private String systemName="核电文控管理系统";

	/**
	 *  发送邮件
	 * @param sendUser
	 * @return
	 * @throws Exception
	 */
	public boolean sendTOIMail(String sendUser,String templateName) throws Exception {

		try {
			logger.debug("发送邮件开始!");

			//创建邮件正文
			Map<String,Object> map=new HashMap<String,Object>();
			//env.getProperty("workflow.todoTaskUrl");
			StringBuilder subject = new StringBuilder(systemName);

//				String docInfo="邮件内容：文件编码，版本，状态！！";
			String todoTaskUrl = CacheManagerOper.getEcmParameter("TOIOperation").getValue();
			//env.getProperty("workflow.todoTaskUrl");
			StringBuilder todoUrl = new StringBuilder(todoTaskUrl);
			map.put("TOIUrl",todoUrl);
			Context context = new Context();
			context.setVariables(map);
			String emailContent = templateEngine.process(templateName, context);
			subject.append("TOI到期提醒");
			mailService.sendHtmlMail(sendUser, subject.toString(),emailContent);

			logger.debug("发送邮件完成");
		} catch (Exception e) {
			e.printStackTrace();
			//throw new Exception("发送邮件开始发生异常！");
		}

		return false;

	}
	/**
	  *  发送邮件
	 * @param sendUser
	 * @return
	 * @throws Exception
	 */
	public boolean sendTaskMail(String sendUser,String taskName,String templateName) throws Exception {

		try {
			   logger.debug("发送邮件开始!");

			 //创建邮件正文
			   Map<String,Object> map=new HashMap<String,Object>();
			   //env.getProperty("workflow.todoTaskUrl");
			   StringBuilder subject = new StringBuilder(systemName);

//				String docInfo="邮件内容：文件编码，版本，状态！！";
				String todoTaskUrl = CacheManagerOper.getEcmParameter("TodoTaskUrl").getValue();
				//env.getProperty("workflow.todoTaskUrl");
				StringBuilder todoUrl = new StringBuilder(todoTaskUrl);
				map.put("taskName",taskName);
				map.put("todoUrl",todoUrl);
			   Context context = new Context();
			   context.setVariables(map);
			   String emailContent = templateEngine.process(templateName, context);
				subject.append("任务超期提醒");
			    mailService.sendHtmlMail(sendUser, subject.toString(),emailContent);

			    logger.debug("发送邮件完成");
			} catch (Exception e) {
				e.printStackTrace();
				 //throw new Exception("发送邮件开始发生异常！");
			}

		return false;

	}

	public boolean sendTaskMail4(String sendUser, List<Map> maps, String templateName) throws Exception {

		try {
			logger.debug("发送邮件开始!");
			//创建邮件正文
			Map<String,Object> map=new HashMap<>();
			map.put("maps",maps);
			//获取配置文件变量
			StringBuilder subject = new StringBuilder(systemName);
			Context context = new Context();
			context.setVariables(map);
			String emailContent = templateEngine.process(templateName, context);
			subject.append("文件状态变动提醒");
			mailService.sendHtmlMail(sendUser,"主题："+ subject,emailContent);

			logger.debug("发送完成");
		} catch (Exception e) {
			e.printStackTrace();
			//throw new Exception("发送邮件开始发生异常！");
		}

		return false;

	}

	public boolean sendMail(String sendUser, List<Map> maps, String templateName, String subject) throws Exception {

		try {
			logger.debug("发送邮件开始!");
			//创建邮件正文
			Map<String,Object> map=new HashMap<>();
			map.put("maps",maps);
			//获取配置文件变量
			Context context = new Context();
			context.setVariables(map);
			String emailContent = templateEngine.process(templateName, context);
			mailService.sendHtmlMail(sendUser,"主题："+ subject,emailContent);

			logger.debug("发送完成");
		} catch (Exception e) {
			e.printStackTrace();
			//throw new Exception("发送邮件开始发生异常！");
		}

		return false;

	}

	public boolean sendMailMultipleUsers(List<String> sendUser, Map<String,Object> maps, String templateName, String subject) throws Exception {
		try {
			logger.debug("发送邮件开始!");
			//获取配置文件变量
			Context context = new Context();
			context.setVariables(maps);
			String emailContent = templateEngine.process(templateName, context);
			mailService.sendHtmlMailMultipleUsers(sendUser,"主题："+ subject,emailContent);

			logger.debug("发送完成");
		} catch (Exception e) {
			e.printStackTrace();
			//throw new Exception("发送邮件开始发生异常！");
		}

		return false;

	}

	public boolean sendMailMultipleUsersMap(List<String> sendUser, Map<String,List<Map>> maps, String templateName, String subject) throws Exception {

		try {
			logger.debug("发送邮件开始!");
			//创建邮件正文
			Map<String,Object> map=new HashMap<>();
			map.put("maps",maps);
			//获取配置文件变量
			Context context = new Context();
			context.setVariables(map);
			String emailContent = templateEngine.process(templateName, context);
			mailService.sendHtmlMailMultipleUsers(sendUser,"主题："+ subject,emailContent);

			logger.debug("发送完成");
		} catch (Exception e) {
			e.printStackTrace();
			//throw new Exception("发送邮件开始发生异常！");
		}

		return false;

	}



}