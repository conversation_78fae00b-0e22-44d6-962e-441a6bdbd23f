package com.zisecm.jobs.service.crossorg;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ecm.core.cache.manager.CacheManagerOper;
import com.ecm.core.entity.*;
import com.ecm.core.exception.AccessDeniedException;
import com.ecm.core.exception.EcmException;
import com.ecm.core.service.AuditService;
import com.ecm.core.service.DocumentService;
import com.ecm.icore.service.IEcmSession;
import com.zisecm.jobs.utils.HttpClientUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class StartWorkflow {
    @Autowired
    DocumentService documentService;
    @Autowired
    SDSendSHService sdSendSHService;

    public void startWorkflowByName(IEcmSession session, String formId, String workflowName) throws EcmException, AccessDeniedException {


        EcmDocument form= documentService.getObjectById(session.getToken(),formId);
        String classificationName= form.getAttributeValue("CLASSIFICATION_NAME")==null
                ?"":form.getAttributeValue("CLASSIFICATION_NAME").toString();
        EcmParameter userNameParam= CacheManagerOper.getEcmParameter("SDWfUserName");
        EcmParameter userPwdParam= CacheManagerOper.getEcmParameter("SDWfUserPwd");
        if(userNameParam==null|| StringUtils.isEmpty(userNameParam.getValue())){
            throw new RuntimeException("SDWfUserName参数未配置！");
        }
        if(userPwdParam==null|| StringUtils.isEmpty(userPwdParam.getValue())){
            throw new RuntimeException("SDWfUserPwd参数未配置！");
        }
        String remoteToken= HttpClientUtils.login("SDBaseurl",userNameParam.getValue(),userPwdParam.getValue());

        EcmCfgProcess process= CacheManagerOper.getCfgProcessCache().get(workflowName);
        String processName= process.getName();
        String processId=process.getProcessId();
        Map<String,Object> args=new HashMap<>();

        args.put("processInstanceId",processId);
        args.put("processConfigName",processName);
        args.put("formId",formId);

        try {
            String rsdataStr=HttpClientUtils.startRemoteWorkflow(remoteToken, JSON.toJSONString(args));
            JSONObject data= JSONObject.parseObject(rsdataStr);
            String processID= data.getString("processID");
            sdSendSHService.sendOrderData(session.getToken(),userNameParam.getValue(),userPwdParam.getValue(),formId,null,processID);
        } catch (Exception e) {
            documentService.newAudit(session.getToken(), "workflow", "workflow"+workflowName, formId, null, e.getMessage());
            throw new RuntimeException(e);
        }
    }
}
