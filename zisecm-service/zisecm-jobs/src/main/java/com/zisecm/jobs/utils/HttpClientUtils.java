package com.zisecm.jobs.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ecm.core.cache.manager.CacheManagerOper;
import com.ecm.core.entity.EcmParameter;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.apache.commons.lang.StringUtils;
import org.apache.http.*;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.ConnectionRequest;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.conn.routing.HttpRoute;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.client.cache.CacheConfig;
import org.apache.http.impl.client.cache.CachingHttpClientBuilder;
import org.apache.http.impl.client.cache.CachingHttpClients;
import org.apache.http.impl.conn.BasicHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HttpContext;
import org.apache.http.util.EntityUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.net.ssl.SSLException;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.InterruptedIOException;
import java.io.Reader;
import java.net.UnknownHostException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * @program: exercise
 * @ClassName: HttpClientUtils
 * @description: Httt请求工具类
 * @author: FPK
 * @create: 2022/4/29 17:37
 */
public class HttpClientUtils {

    // httpClient配置
    public static final int SOCKET_TIME_OUT = 50000; // 设置超时时间
    public static final int CONNECT_TIME_OUT = 30000; // 设置链接超时时间
    public static final int MAX_CACHE_ENTRIES = 1000; // 设置缓存
    public static final long MAX_OBJECT_SIZE = 8192; // 最多缓存对象大小
    public static final int OUT_TIME_NUM = 5; // http请求重试次数
    private static CloseableHttpClient httpClient = null;

    public static CloseableHttpClient getCloseableHttpClient() {
        if (httpClient == null) {
            httpClient = HttpClients.createDefault();
        }
        return httpClient;
    }

    public static CloseableHttpClient getCloseableHttpClient(CacheConfig cacheConfig,RequestConfig requestConfig, HttpRequestRetryHandler myRetryHandler) {
        // 设置缓存
        if (httpClient == null) {
            CachingHttpClientBuilder custom = CachingHttpClients.custom();
            if (cacheConfig != null) {
                custom.setCacheConfig(getCacheConfig());
            }
            if (requestConfig != null) {
                custom.setDefaultRequestConfig(getRequestConfig());
            }
            if (myRetryHandler != null) {
                custom.setRetryHandler(myRetryHandler);
            }
            httpClient = custom.build();
        }
        return httpClient;
    }

    /**
     * @Description 获取重试机制的HttpClinet
     * @param outTimeNum 超时次数
     * @Return
     * @Date 2022/4/29 18:06
     * <AUTHOR>
     */
    public static CloseableHttpClient getCloseableHttpClient(int outTimeNum) {
        // 设置缓存
        if (httpClient == null) {
            CachingHttpClientBuilder custom = CachingHttpClients.custom();
            custom.setCacheConfig(getCacheConfig());
            custom.setDefaultRequestConfig(getRequestConfig());
            custom.setRetryHandler(getHttpRequestRetryHandler(outTimeNum));
            httpClient = custom.build();
        }
        return httpClient;
    }

    /**
     * @Description 获取重试机制的HttpClinet
     * @param outTimeNum 超时次数
     * @Return
     * @Date 2022/4/29 18:06
     * <AUTHOR>
     */
    public static HttpRequestRetryHandler getHttpRequestRetryHandler(int outTimeNum) {
        return new HttpRequestRetryHandler() {
            // false:重试,true:不重试
            public boolean retryRequest(
                    IOException exception,
                    int executionCount,
                    HttpContext context) {
                if (executionCount > outTimeNum) {
                    // 如果超过最大重试次数，不重试
                    return false;
                }
                if (exception instanceof InterruptedIOException) {
                    // 超时
                    return false;
                }
                if (exception instanceof UnknownHostException) {
                    // 未知的主机
                    return false;
                }
                if (exception instanceof ConnectTimeoutException) {
                    // 连接拒绝
                    return false;
                }
                if (exception instanceof SSLException) {
                    // SSL握手例外
                    return false;
                }
                HttpClientContext clientContext = HttpClientContext.adapt(context);
                HttpRequest request = clientContext.getRequest();
                boolean idempotent = !(request instanceof HttpEntityEnclosingRequest);
                if (idempotent) {
                    // 如果认为请求是幂等的，则重试
                    return true;
                }
                return false;
            }
        };
    }

    public static ResponseHandler<JSONObject> getResponseHandler(){

        ResponseHandler<JSONObject> responseHandler = new ResponseHandler<JSONObject>() {
            @Override
            public JSONObject handleResponse(
                    final HttpResponse response) throws IOException {
                JSONObject result = new JSONObject();
                StatusLine statusLine = response.getStatusLine();
                HttpEntity entity = response.getEntity();
                result.put("status", statusLine.getStatusCode());
                result.put("headers", response.getAllHeaders());
                if (statusLine.getStatusCode() >= 300) {
                    result.put("body", response.getEntity());
//                    throw new HttpResponseException(
//                            statusLine.getStatusCode(),
//                            statusLine.getReasonPhrase());
                }
                if (entity == null) {
                    result.put("body", "Response contains no content");
                    return result;
//                    throw new ClientProtocolException("Response contains no content");
                }
                Gson gson = new GsonBuilder().create();
                ContentType contentType = ContentType.getOrDefault(entity);
                Charset charset = contentType.getCharset();
                Reader reader = null;
                if (charset == null) {
                    reader = new InputStreamReader(entity.getContent());
                } else {
                    reader = new InputStreamReader(entity.getContent(), charset);
                }
                if(statusLine.getStatusCode() == 200) {
                    result.put("body", gson.fromJson(reader, JSONObject.class));
                    return result;
                }
                return gson.fromJson(reader, JSONObject.class);
            }
        };
        return responseHandler;
    }

    /**
     * @Description 超时设置
     * @Date 2022/4/30 15:42
     * <AUTHOR>
     */
    public static RequestConfig getRequestConfig(){
         return RequestConfig.custom()
                .setSocketTimeout(SOCKET_TIME_OUT)
                .setConnectTimeout(CONNECT_TIME_OUT)
                .build();
    }

    /**
     * @Description 缓存设置
     * @Return
     * @Date 2022/4/30 15:48
     * <AUTHOR>
     */
    public static CacheConfig getCacheConfig(){
         return CacheConfig.custom()
                .setMaxCacheEntries(MAX_CACHE_ENTRIES)
                .setMaxObjectSize(MAX_OBJECT_SIZE)
                .build();
    }

    public static HttpClientConnectionManager getHttpClientConnectionManager() throws IOException, ExecutionException, InterruptedException {
        HttpClientContext context = HttpClientContext.create();
        HttpClientConnectionManager connMrg = new BasicHttpClientConnectionManager();
        HttpRoute route = new HttpRoute(new HttpHost("localhost", 8080));
        // 新连接请求。这可能是一个漫长的过程
        ConnectionRequest connRequest = connMrg.requestConnection(route, null);
        // 等待连接长达60秒
        HttpClientConnection conn = connRequest.get(100000, TimeUnit.SECONDS);
        try {
            // 如果不开放
            if (!conn.isOpen()) {
                // 根据路由信息建立连接
                connMrg.connect(conn, route, 2000, context);
                // 并将其标记为路线完成
                connMrg.routeComplete(conn, route, context);
            }
            // 利用连接做一些有用的事情。
        } finally {
            connMrg.releaseConnection(conn, null, 2, TimeUnit.MINUTES);
            return connMrg;
        }
    }

    public static JSONObject get(String url) throws IOException {
        HttpGet httpget = new HttpGet(url);
        httpget.setConfig(HttpClientUtils.getRequestConfig());
        return getCloseableHttpClient(OUT_TIME_NUM).execute(httpget, getResponseHandler());
    }

    public static JSONObject get(String url, Map<String, String> hreads) throws IOException {
        HttpGet httpget = new HttpGet(url);
        hreads.forEach((k,v) -> httpget.addHeader(k,v));
        httpget.setConfig(HttpClientUtils.getRequestConfig());
        return getCloseableHttpClient(OUT_TIME_NUM).execute(httpget, getResponseHandler());
    }

    public static JSONObject post(String url, List<NameValuePair> nvps, Map<String, String> hreads) throws IOException {
        HttpPost httpPost = new HttpPost(url);
        StringEntity entity = new StringEntity("important message",
                ContentType.create("application/json", Consts.UTF_8));
        entity.setChunked(true);
        httpPost.setEntity(entity);
        if (hreads != null && !hreads.isEmpty()) {
            hreads.forEach((k, v) -> httpPost.addHeader(k, v));
        }
        if (nvps != null && !nvps.isEmpty()) {
            httpPost.setEntity(new UrlEncodedFormEntity(nvps));
        }
        return getCloseableHttpClient(OUT_TIME_NUM).execute(httpPost, getResponseHandler());
    }

    public static String login(String baseUrlName,String username,String password) {
        HttpPost httpPost=null;
        try {//SDBaseurl
            EcmParameter param= CacheManagerOper.getEcmParameter(baseUrlName);
            if(param==null|| StringUtils.isEmpty(param.getValue())){
                throw new Exception(baseUrlName+"参数没有配置，请联系管理员！");
            }
            httpPost = new HttpPost(param.getValue()+"/userLogin");
            RequestConfig timeoutConfig = RequestConfig.custom()
                    .setConnectTimeout(120000).setConnectionRequestTimeout(30000)
                    .setSocketTimeout(120000).build();
            httpPost.setConfig(timeoutConfig);
            //httpPost.setHeader("Content-Type","www-form-urlencoded");
            List<BasicNameValuePair> params = new ArrayList<BasicNameValuePair>();

            params.add(new BasicNameValuePair("username", username));
            params.add(new BasicNameValuePair("password", password));

            httpPost.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
            if (httpClient == null) {
                httpClient = HttpClients.createDefault();
            }
            HttpResponse response = httpClient.execute(httpPost);

            StatusLine statusLine = response.getStatusLine();
            int statusCode = statusLine.getStatusCode();
            // 请求成功
            if (statusCode == 200) {
                HttpEntity responseResult = response.getEntity();
                String responseEntityStr = EntityUtils.toString(responseResult, "UTF-8");
                JSONObject jsonResult = JSON.parseObject(responseEntityStr);
                String data = jsonResult.getString("data");
                jsonResult = JSON.parseObject(data);
                if(jsonResult.containsKey("token")) {
                    return jsonResult.getString("token");
                }

            }
        }catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String startRemoteWorkflow(String token, String metaData) throws Exception
    {
//        /workflow/startWorkflow
        EcmParameter param= CacheManagerOper.getEcmParameter("SDBaseurl");
        if(param==null||StringUtils.isEmpty(param.getValue())){
            throw new Exception("SDBaseurl参数没有配置，请联系管理员！");
        }

        HttpPost httpPost = new HttpPost(param.getValue()+"/workflow/startWorkflow");

        // 设置请求头
        httpPost.setHeader("Content-Type", "application/json");
        httpPost.setHeader("Accept", "application/json");
        httpPost.addHeader("token", token);
        // 设置请求体
        StringEntity entity = new StringEntity(metaData, "UTF-8");
        httpPost.setEntity(entity);

        CloseableHttpResponse response =httpClient.execute(httpPost);
        if(response.getStatusLine().getStatusCode()==200){
            HttpEntity responseResult = response.getEntity();
            String responseEntityStr = EntityUtils.toString(responseResult, "UTF-8");
            com.alibaba.fastjson2.JSONObject jsonResult = com.alibaba.fastjson2.JSON.parseObject(responseEntityStr);
            String data = jsonResult.getString("data");
            jsonResult = com.alibaba.fastjson2.JSON.parseObject(data);
            if(jsonResult.containsKey("code")) {
                int code = jsonResult.getInteger("code");
                if(code!=1) {
                    throw new Exception("流程发起失败");
                }
                return data;
            }
        }
        return null;
    }

    public static String executeRestWithFile(String token, String baseUrlName,String url, String metaName, String metaData, String mainFileName, MultipartFile mainFile,
                                             String attachFileName, MultipartFile[] attachFiles) throws Exception {
        getCloseableHttpClient();

        EcmParameter param= CacheManagerOper.getEcmParameter(baseUrlName);
        if(param==null||StringUtils.isEmpty(param.getValue())){
            throw new Exception("SDBaseurl参数没有配置，请联系管理员！");
        }

        HttpPost post=new HttpPost(param.getValue()+url);
        MultipartEntityBuilder builder = MultipartEntityBuilder.create();
        builder.addPart(metaName,
                new StringBody(metaData, ContentType.APPLICATION_JSON));
        if (mainFile != null) {
//            builder.addPart("mainFile",
//                    new FileBody(mainFile, ContentType.MULTIPART_FORM_DATA,
//                            mainFile.getName()));
            builder.addBinaryBody(mainFileName,mainFile.getInputStream(),ContentType.create("multipart/form-data"), mainFile.getName());
        }
        if (attachFiles != null) {
            for (MultipartFile attachFile : attachFiles) {
//                builder.addPart("attachFiles",
//                        new FileBody(attachFile, ContentType.MULTIPART_FORM_DATA,
//                                attachFile.getName()));
                builder.addBinaryBody(attachFileName,attachFile.getInputStream(),ContentType.create("multipart/form-data"), attachFile.getName());
            }
        }
        post.addHeader("token", token);
        post.setEntity(builder.build());
        try {
            CloseableHttpResponse response =httpClient.execute(post);
            if(response.getStatusLine().getStatusCode()==200){
                HttpEntity responseResult = response.getEntity();
                String responseEntityStr = EntityUtils.toString(responseResult, "UTF-8");
                return responseEntityStr;
//                com.alibaba.fastjson2.JSONObject jsonResult = com.alibaba.fastjson2.JSON.parseObject(responseEntityStr);
//                String data = jsonResult.getString("data");
//                return data;
//                jsonResult = com.alibaba.fastjson2.JSON.parseObject(data);
//                if(jsonResult.containsKey("code")) {
//                    int code = jsonResult.getInteger("code");
//                    if(code==1) {
//                        return data;
////                        if(jsonResult.containsKey("id")) {
////                            logger.debug(jsonResult.getString("id"));
////                            return jsonResult.getString("id");
////                        }
//                    }
//                }
            }

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }
}
