<template>
  <div class="task-deliverable-list">
    <!-- 交付物头部 -->
    <div class="deliverable-header">
      <div class="header-left">
        <h4>
          <i class="el-icon-document"></i>
          交付物管理
          <el-badge :value="deliverableList.length" class="item-badge" type="primary" />
        </h4>
        <div class="statistics" v-if="statistics">
          <el-tag size="mini" type="success">已完成: {{ statistics.completed || 0 }}</el-tag>
          <el-tag size="mini" type="warning">进行中: {{ statistics.inProgress || 0 }}</el-tag>
          <el-tag size="mini" type="info">未开始: {{ statistics.notStarted || 0 }}</el-tag>
          <el-tag size="mini" type="danger" v-if="statistics.overdueCount > 0">
            逾期: {{ statistics.overdueCount }}
          </el-tag>
        </div>
      </div>
      <div class="header-right">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAddDeliverable"
          :disabled="!taskId">
          添加交付物
        </el-button>
        <el-button
          size="mini"
          icon="el-icon-refresh"
          @click="loadDeliverables">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 交付物列表 -->
    <div class="deliverable-content" v-loading="loading">
      <div v-if="deliverableList.length === 0" class="empty-state">
        <i class="el-icon-document-copy"></i>
        <p>暂无交付物</p>
        <el-button
          type="text"
          @click="handleAddDeliverable"
          :disabled="!taskId">
          添加第一个交付物
        </el-button>
      </div>

      <div v-else class="deliverable-items">
        <div
          v-for="deliverable in deliverableList"
          :key="deliverable.id"
          class="deliverable-item"
          :class="{ 'overdue': deliverable.isOverdue }">

          <!-- 交付物基本信息 -->
          <div class="item-header">
            <div class="item-title">
              <el-tag
                :type="getStatusType(deliverable.status)"
                size="mini"
                class="status-tag">
                {{ deliverable.statusText }}
              </el-tag>
              <span class="deliverable-name">{{ deliverable.deliverableName }}</span>
              <el-tag
                v-if="deliverable.type"
                size="mini"
                class="type-tag">
                {{ deliverable.type }}
              </el-tag>
              <el-tag
                v-if="deliverable.priority === 1"
                type="danger"
                size="mini"
                class="priority-tag">
                高优先级
              </el-tag>
            </div>
            <div class="item-actions">
              <el-button-group>
                <el-button
                  size="mini"
                  icon="el-icon-view"
                  @click="handleViewDeliverable(deliverable)"
                  title="查看详情">
                </el-button>
                <el-button
                  size="mini"
                  icon="el-icon-edit"
                  @click="handleEditDeliverable(deliverable)"
                  title="编辑">
                </el-button>
                <el-button
                  size="mini"
                  icon="el-icon-delete"
                  type="danger"
                  @click="handleDeleteDeliverable(deliverable)"
                  title="删除">
                </el-button>
              </el-button-group>
            </div>
          </div>

          <!-- 交付物详细信息 -->
          <div class="item-content">
            <div class="item-info">
              <div class="info-row">
                <span class="label">编码:</span>
                <span class="value">{{ deliverable.deliverableCode }}</span>
              </div>
              <div class="info-row" v-if="deliverable.assigneeName">
                <span class="label">负责人:</span>
                <span class="value">{{ deliverable.assigneeName }}</span>
              </div>
              <div class="info-row" v-if="deliverable.plannedDeliveryDate">
                <span class="label">计划交付:</span>
                <span class="value">{{ formatDate(deliverable.plannedDeliveryDate) }}</span>
              </div>
              <div class="info-row" v-if="deliverable.actualDeliveryDate">
                <span class="label">实际交付:</span>
                <span class="value">{{ formatDate(deliverable.actualDeliveryDate) }}</span>
              </div>
            </div>

            <!-- 进度条 -->
            <div class="progress-section">
              <div class="progress-header">
                <span class="progress-label">进度</span>
                <span class="progress-value">{{ deliverable.progress || 0 }}%</span>
              </div>
              <el-progress
                :percentage="deliverable.progress || 0"
                :status="getProgressStatus(deliverable.status, deliverable.progress)"
                :stroke-width="6">
              </el-progress>
            </div>

            <!-- 描述 -->
            <div v-if="deliverable.description" class="description">
              <span class="label">描述:</span>
              <p class="desc-text">{{ deliverable.description }}</p>
            </div>

            <!-- 文件信息 -->
            <div v-if="deliverable.fileName" class="file-info">
              <i class="el-icon-paperclip"></i>
              <span class="file-name">{{ deliverable.fileName }}</span>
              <span v-if="deliverable.fileSize" class="file-size">
                ({{ formatFileSize(deliverable.fileSize) }})
              </span>
              <el-button
                type="text"
                size="mini"
                @click="handleDownloadFile(deliverable)">
                下载
              </el-button>
            </div>

            <!-- 快速操作 -->
            <div class="quick-actions">
              <el-button-group size="mini">
                <el-button
                  v-if="deliverable.status === 0"
                  type="primary"
                  @click="handleUpdateStatus(deliverable, 1)">
                  开始
                </el-button>
                <el-button
                  v-if="deliverable.status === 1"
                  type="success"
                  @click="handleUpdateStatus(deliverable, 2)">
                  完成
                </el-button>
                <el-button
                  v-if="deliverable.status !== 2"
                  @click="handleUpdateProgress(deliverable)">
                  更新进度
                </el-button>
                <el-button
                  @click="handleUploadFile(deliverable)">
                  上传文件
                </el-button>
              </el-button-group>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 交付物编辑对话框 - 暂时注释掉 -->
    <!--
    <DeliverableDialog
      :visible.sync="dialogVisible"
      :deliverable="currentDeliverable"
      :task-id="taskId"
      :is-edit="isEdit"
      @success="handleDialogSuccess"
    />
    -->

    <!-- 进度更新对话框 -->
    <el-dialog
      title="更新进度"
      :visible.sync="progressDialogVisible"
      width="400px">
      <div class="progress-update">
        <el-form>
          <el-form-item label="当前进度">
            <el-slider
              v-model="currentProgress"
              :min="0"
              :max="100"
              show-input
              :format-tooltip="val => `${val}%`">
            </el-slider>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="progressDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmProgress">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 暂时注释掉DeliverableDialog的导入，先让基础功能工作
// import DeliverableDialog from './DeliverableDialog.vue'

export default {
  name: 'TaskDeliverableList',

  components: {
    // DeliverableDialog
  },

  props: {
    taskId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      loading: false,
      deliverableList: [],
      statistics: null,
      dialogVisible: false,
      currentDeliverable: null,
      isEdit: false,
      progressDialogVisible: false,
      currentProgress: 0,
      progressDeliverable: null
    }
  },

  watch: {
    taskId: {
      immediate: true,
      handler(newTaskId) {
        if (newTaskId) {
          this.loadDeliverables()
        }
      }
    }
  },

  methods: {
    /**
     * 加载交付物列表
     */
    async loadDeliverables() {
      console.log('开始加载交付物列表，任务ID:', this.taskId)

      if (!this.taskId) {
        console.warn('任务ID为空，无法加载交付物')
        return
      }

      this.loading = true
      try {
        const data = {
          url: `/projectDeliverable/list/${this.taskId}`,
          pageIndex: 1,
          pageSize: 100,
          isReqBody: true
        }

        console.log('发送API请求:', data)
        const response = await this.$restful(data)
        console.log('API响应:', response)

        if (response && response.code === 200) {
          this.deliverableList = response.data || []
          this.statistics = response.statistics || {}

          console.log('交付物列表加载成功:', this.deliverableList.length, '条记录')
          console.log('统计信息:', this.statistics)
        } else {
          console.error('API返回错误:', response)
          this.$message.error(response?.msg || '加载交付物列表失败')
        }
      } catch (error) {
        console.error('加载交付物列表失败:', error)
        this.$message.error('加载交付物列表失败: ' + error.message)
      } finally {
        this.loading = false
      }
    },

    /**
     * 添加交付物
     */
    handleAddDeliverable() {
      // 暂时用简单提示替代对话框
      this.$message.info('添加交付物功能开发中...')
      console.log('添加交付物，任务ID:', this.taskId)
    },

    /**
     * 查看交付物详情
     */
    handleViewDeliverable(deliverable) {
      // 暂时用简单提示替代对话框
      this.$message.info(`查看交付物: ${deliverable.deliverableName}`)
      console.log('查看交付物详情:', deliverable)
    },

    /**
     * 编辑交付物
     */
    handleEditDeliverable(deliverable) {
      // 暂时用简单提示替代对话框
      this.$message.info(`编辑交付物: ${deliverable.deliverableName}`)
      console.log('编辑交付物:', deliverable)
    },

    /**
     * 删除交付物
     */
    async handleDeleteDeliverable(deliverable) {
      try {
        await this.$confirm(`确定要删除交付物"${deliverable.deliverableName}"吗？`, '确认删除', {
          type: 'warning'
        })

        const data = {
          url: '/projectDeliverable/delete',
          id: deliverable.id,
          isReqBody: true
        }

        const response = await this.$restful(data)

        if (response && response.code === 200) {
          this.$message.success('删除成功')
          this.loadDeliverables()
        } else {
          this.$message.error(response?.msg || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除交付物失败:', error)
          this.$message.error('删除失败')
        }
      }
    },

    /**
     * 更新状态
     */
    async handleUpdateStatus(deliverable, status) {
      try {
        const data = {
          url: '/projectDeliverable/updateStatus',
          id: deliverable.id,
          status: status,
          isReqBody: true
        }

        const response = await this.$restful(data)

        if (response && response.code === 200) {
          this.$message.success('状态更新成功')
          this.loadDeliverables()
        } else {
          this.$message.error(response?.msg || '状态更新失败')
        }
      } catch (error) {
        console.error('更新状态失败:', error)
        this.$message.error('状态更新失败')
      }
    },

    /**
     * 更新进度
     */
    handleUpdateProgress(deliverable) {
      this.progressDeliverable = deliverable
      this.currentProgress = deliverable.progress || 0
      this.progressDialogVisible = true
    },

    /**
     * 确认进度更新
     */
    async handleConfirmProgress() {
      try {
        const data = {
          url: '/projectDeliverable/updateProgress',
          id: this.progressDeliverable.id,
          progress: this.currentProgress,
          isReqBody: true
        }

        const response = await this.$restful(data)

        if (response && response.code === 200) {
          this.$message.success('进度更新成功')
          this.progressDialogVisible = false
          this.loadDeliverables()
        } else {
          this.$message.error(response?.msg || '进度更新失败')
        }
      } catch (error) {
        console.error('更新进度失败:', error)
        this.$message.error('进度更新失败')
      }
    },

    /**
     * 上传文件
     */
    handleUploadFile(deliverable) {
      // TODO: 实现文件上传功能
      this.$message.info('文件上传功能开发中...')
    },

    /**
     * 下载文件
     */
    handleDownloadFile(deliverable) {
      // TODO: 实现文件下载功能
      this.$message.info('文件下载功能开发中...')
    },

    /**
     * 对话框成功回调
     */
    handleDialogSuccess() {
      this.dialogVisible = false
      this.loadDeliverables()
    },

    /**
     * 获取状态类型
     */
    getStatusType(status) {
      const types = ['info', 'warning', 'success', 'danger']
      return types[status] || 'info'
    },

    /**
     * 获取进度状态
     */
    getProgressStatus(status, progress) {
      if (status === 2) return 'success'
      if (status === 3) return 'exception'
      if (progress >= 100) return 'success'
      return null
    },

    /**
     * 格式化日期
     */
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleDateString('zh-CN')
    },

    /**
     * 格式化文件大小
     */
    formatFileSize(size) {
      if (!size) return ''
      if (size < 1024) return size + ' B'
      if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB'
      if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + ' MB'
      return (size / (1024 * 1024 * 1024)).toFixed(1) + ' GB'
    }
  }
}
</script>

<style scoped>
.task-deliverable-list {
  margin-top: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fff;
}

.deliverable-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e4e7ed;
  background: #f8f9fa;
}

.header-left h4 {
  margin: 0;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.item-badge {
  margin-left: 8px;
}

.statistics {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.deliverable-content {
  min-height: 200px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.deliverable-items {
  padding: 0;
}

.deliverable-item {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 20px;
  transition: background-color 0.3s;
}

.deliverable-item:hover {
  background-color: #f8f9fa;
}

.deliverable-item.overdue {
  border-left: 4px solid #f56c6c;
  background-color: #fef0f0;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.item-title {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.deliverable-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.status-tag, .type-tag, .priority-tag {
  font-size: 12px;
}

.item-content {
  margin-left: 0;
}

.item-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
  margin-bottom: 12px;
}

.info-row {
  display: flex;
  align-items: center;
  font-size: 13px;
}

.info-row .label {
  color: #909399;
  margin-right: 8px;
  min-width: 60px;
}

.info-row .value {
  color: #606266;
}

.progress-section {
  margin-bottom: 12px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-label {
  font-size: 13px;
  color: #909399;
}

.progress-value {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.description {
  margin-bottom: 12px;
}

.description .label {
  color: #909399;
  font-size: 13px;
}

.desc-text {
  margin: 4px 0 0 0;
  color: #606266;
  font-size: 13px;
  line-height: 1.5;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 13px;
}

.file-name {
  color: #409eff;
}

.file-size {
  color: #909399;
}

.quick-actions {
  display: flex;
  justify-content: flex-end;
}

.progress-update {
  padding: 20px 0;
}
</style>
