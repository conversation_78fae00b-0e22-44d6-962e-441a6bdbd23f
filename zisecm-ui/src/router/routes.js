import layoutHeaderAside from '@/layout/header-aside'
import todoTask from '@/views/workflow/task/TodoTask'
import workflowAudit from '@/views/workflow/WorkflowAudit'
import doneTask from '@/views/workflow/task/DoneTask'
import toReadTask from '@/views/workflow/task/ToReadTask'
import haveReadTask from '@/views/workflow/task/HaveReadTask'
import myWorkflow from '@/views/workflow/task/MyWorkflow'
import HomePageCommonFunctions from "@/router/modules/HomePageCommonFunctions";
import DistributeWorkflow from './modules/DistributeWorkflow';
import CreateDocument from './modules/CreateDocument';
import reportManagement from "@/router/modules/reportManagement";
import businessForm from "@/router/modules/businessForm";
import docServiceForm from "@/router/modules/docServiceForm";
import emergencySite from "@/router/modules/emergencySite";
import ProblemViews from "@/router/modules/ProblemViews";
import basicData from "@/router/modules/basicData";
import ArchiveMgt from "@/router/modules/archiveMgt";
import topicManagement from "@/router/modules/topicManagement";
import projectMgt from "@/router/modules/projectMgt";
// 由于懒加载页面太多的话会造成webpack热更新太慢，所以开发环境不使用懒加载，只有生产环境使用懒加载
const _import = require('@/libs/util.import.' + process.env.NODE_ENV)
// ljjun:
// meta:{
//   fullScreen: 设置页面全屏，如果不设置此参数将默认为false不进行满屏化展示，如果设置
//               showLeftMenu：点击控制菜单收缩/放开按钮会展示左侧菜单，
//               hideLeftMenu：点击控制菜单收缩/放开按钮不会展示左侧菜单
// }

/**
 * 在主框架内显示
 */
const frameIn = [
  {
    path: '/',
    redirect: { name: 'index' },
    component: layoutHeaderAside,
    children: [
      {
        meta: {
          title:'个人中心'
        },
        path: '/personalInformationCenter',
        name: 'personalInformationCenter',
        component: _import('system/index/childPages/PersonalInformation/personalInformationCenter.vue')
      },
      {
        meta: {
          title:'卡片搜索'
        },
        path: '/search/cardSearch',
        name: 'cardSearch',
        component: () => import('@/components/card-search/CardSearchV2.vue')
      },
      {
        meta: {
          title:'分类搜索'
        },
        path: '/search/classifySearch',
        name: 'classifySearch',
        component: () => import('@/components/classify-search/ClassifySearchV2.vue')
      },
      {
        path: 'typeSearch',
        name: 'typeSearch',
        meta: {
          title: '分类查询',
          auth: true
        },
        component: _import('km/search')
      },
      {
        path: 'contentSearch/:text',
        name: 'contentSearch',
        meta: {
          title: '全文搜索',
          auth: true,
          cache: true
        },
        component: _import('km/contentSearch')
      },{
        path: 'contentSearch',
        name: 'contentSearch',
        meta: {
          title: '全文搜索',
          auth: true,
          cache: true
        },
        component: _import('km/contentSearch')
      },
      {
        path: 'folderViewer',
        name: 'folderViewer',
        meta: {
          title: '文件夹查看',
          auth: true
        },
        component: _import('dc/FolderViewer')
      },
      // 系统 前端日志
      {
        path: 'log',
        name: 'log',
        meta: {
          title: '前端日志',
          auth: true
        },
        component: _import('system/log')
      },
      // 刷新页面 必须保留
      {
        path: 'refresh',
        name: 'refresh',
        hidden: true,
        component: _import('system/function/refresh')
      },
      // 页面重定向 必须保留
      {
        path: 'redirect/:route*',
        name: 'redirect',
        hidden: true,
        component: _import('system/function/redirect')
      },
      {
        path: "/NoPermission",
        name: "无权限",
        component: _import('system/NoPermission')
      },
    ]
  },

  // 首页
  {
    path: '/index',
    name: 'index',
    redirect: { name: 'home' },
    meta: {
      auth: true
    },
    component: layoutHeaderAside,
    children: [
      {
        path: '',
        name: 'home',
        meta: {
          title: '首页',
          auth: true,
          cache : true, // 首页做缓存
          fullScreen: 'hideLeftMenu'
        },
        component: _import('system/index')
      },
      {
        meta: {
          title: '系统管理',
          auth: true,
          fullScreen: 'hideLeftMenu'
        },
        path: '/sysmanager',
        name: 'sysmanager',
        component: () => import('@/views/system/SystemManager.vue'),
      },
      // 待办任务
      {
        path: 'todoTask',
        name: 'todoTask',
        meta: {
          title: '待办任务',
          auth: true
        },
        component: todoTask
      },
      // 已办任务
      {
        path: 'doneTask',
        name: 'doneTask',
        meta: {
          title: '已办任务',
          auth: true
        },
        component: doneTask
      },
      // 我的流程
      {
        path: 'myWorkflow',
        name: 'myWorkflow',
        meta: {
          title: '我的工作流',
          auth: true
        },
        component: myWorkflow
      },

      // 流程跟踪
      {
        path: 'workflowAudit',
        name: 'workflowAudit',
        meta: {
          title: '流程跟踪',
          auth: true
        },
        component: workflowAudit
      },
      // 待办任务
      {
        path: 'tobeReadTask',
        name: 'tobeReadTask',
        meta: {
          title: '待阅任务',
          auth: true
        },
        component: toReadTask
      },
      // 已办任务
      {
        path: 'haveReadTask',
        name: 'haveReadTask',
        meta: {
          title: '已阅任务',
          auth: true
        },
        component: haveReadTask
      },
      {
        meta: {
          title: '草稿箱',
          auth: true
        },
        path: 'drafts',
        name: 'draft',
        component: () => import('@/views/workflow/Drafts.vue'),
      },
      ...HomePageCommonFunctions
    ],
  },


  // 分类检索
  {
    //分类检索横向菜单
    path: '/classificationRetrieval',
    name: 'classificationRetrievalHome',
    redirect: { name: 'classificationRetrieval' },
    meta: {
      auth: true,
    },
    component: layoutHeaderAside,
    children: [
      // 分类检索页面
      {
        path: 'search/:id',
        name: 'classificationRetrieval',
        meta: {
          title: '文档搜索',
          fullScreen: "hideLeftMenu",
          auth: true,
          cache: true
        },
        component: _import('km/classificationRetrieval')
      },
    ]
  },
  //demo page
  {
    path: '/page',
    // name: 'page',
    redirect: { name: 'page1' },
    meta: {
      title: '页面',
      auth: true
    },
    component: layoutHeaderAside,
    children: [
      // 演示页面
      {
        path: 'page1',
        name: 'page1',
        meta: {
          title: '页面 1',
          auth: true
        },
        component: _import('demo/page1')
      },
      {
        path: 'page2',
        name: 'page2',
        meta: {
          title: '页面 2',
          auth: true
        },
        component: _import('demo/page2')
      },
      {
        path: 'page3',
        name: 'page3',
        meta: {
          title: '页面 3',
          auth: true
        },
        component: _import('demo/page3')
      },
      {
        path: 'page5',
        name: 'page5',
        meta: {
          title: '页面 5',
          auth: true
        },
        component: _import('demo/page5')
      },
    ]
  },
  ...DistributeWorkflow,
  ...CreateDocument,
  ...businessForm,
  ...reportManagement,
  ...docServiceForm,
  ...emergencySite,
  ...ProblemViews,
  ...topicManagement,
  ...basicData,
  ...ArchiveMgt
]

/**
 * 在主框架之外显示
 */
const frameOut = [
  // 登录
  {
    path: '/login',
    name: 'login',
    component: _import('system/login')
  },
  {
    path: '/adminLogin',
    name: 'adminLogin',
    component: _import('system/login')
  },
  //显示文件  todo:此处路由在IE下存在问题，会导致IE访问失败
  {
    path: '/ViewDoc',
    name: 'ViewDoc',
    meta: {
      title: '文件查看',
      auth: true
    },
    component: _import('dc/ViewDoc.vue')
  },
  // 添加小写的viewdoc路由别名
  {
    path: '/viewdoc',
    name: 'viewdoc',
    meta: {
      title: '文件查看',
      auth: true
    },
    component: _import('dc/ViewDoc.vue')
  },
  {
    path: '/sapopenfile.jsp',
    name: 'sapViewDoc',
    meta: {
      title: 'SAP路由跳转',
      auth: true
    },
    component: _import('dc/sapViewDoc.vue')
  },
  //基础功能
  {
    path: '/SimpleWord/test',
    name: 'test',
    meta: {
      title: '在线编辑Word',
      auth: false
    },
    component: _import('SimpleWord/test.vue')
  },


]

/**
 * 错误页面
 */
const errorPage = [
  {
    path: '*',
    name: '404',
    component: _import('system/error/404')
  }
]

// 导出需要显示菜单的
export const frameInRoutes = frameIn

// 重新组织后导出
export default [
  ...frameIn,
  ...frameOut,
  ...errorPage
]
