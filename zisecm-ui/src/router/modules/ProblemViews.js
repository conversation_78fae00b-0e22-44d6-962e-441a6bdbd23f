import layoutHeaderAside from '@/layout/header-aside'

// 由于懒加载页面太多的话会造成webpack热更新太慢，所以开发环境不使用懒加载，只有生产环境使用懒加载
const _import = require('@/libs/util.import.' + process.env.NODE_ENV)

const meta = { auth: true , cache: true}

export default [
  //程序问题反馈
  {
    path: '/ProblemViews/home',
    name: 'ProblemViewsHome',
    redirect: { name: 'ProblemViewsHome' },
    meta: {
      ...meta,
      title:'问题反馈',
    },
    component: layoutHeaderAside,
    children:[
      {
        path: '',
        name: 'ProblemViewsHome',
        meta: {
          ...meta,
          title:'问题反馈',
          fullScreen: 'hideLeftMenu',
        },
        component: _import('general/home'),
      },
    ]
  },
  //程序问题反馈
  {
    path: '/ProblemViews',
    name: 'ProblemViews',
    redirect: { name: 'ProblemViews' },
    meta: {
      ...meta,
      title:'程序问题反馈',
    },
    component: layoutHeaderAside,
    children:[
      {
        path: '',
        name: 'ProblemViews',
        meta: {
          ...meta,
          title:'程序问题反馈',
          fullScreen: 'hideLeftMenu',
        },
        component: _import('general/ProblemView'),
      },
    ]
  },
  //文档问题反馈
  {
    path: '/DocProblemViews',
    name: 'DocProblemViews',
    redirect: { name: 'DocProblemViews' },
    meta: {
      ...meta,
      title:'文档问题反馈',
    },
    component: layoutHeaderAside,
    children:[
      {
        path: '',
        name: 'DocProblemViews',
        meta: {
          ...meta,
          title:'文档问题反馈',
          fullScreen: 'hideLeftMenu',
        },
        component: _import('general/DocProblemView'),
      },
    ]
  },
  //系统问题反馈
  {
    path: '/SystemProblemViews',
    name: 'SystemProblemViews',
    redirect: { name: 'SystemProblemViews' },
    meta: {
      ...meta,
      title:'系统问题反馈',
    },
    component: layoutHeaderAside,
    children:[
      {
        path: '',
        name: 'SystemProblemViews',
        meta: {
          ...meta,
          title:'系统问题反馈',
          fullScreen: 'hideLeftMenu',
        },
        component: _import('general/SystemProblemView'),
      },
    ]
  },
//5年升版计划报表
  // {
  //   path: '/FiveYearPlanViews',
  //   name: 'FiveYearPlanViews',
  //   redirect: { name: 'FiveYearsViews' },
  //   meta: {
  //     ...meta,
  //     title:'5年升版报表',
  //   },
  //   component: layoutHeaderAside,
  //   children:[
  //     {
  //       path: '',
  //       name: 'FiveYearsViews',
  //       meta: {
  //         ...meta,
  //         title:'5年升版报表',
  //         fullScreen: 'hideLeftMenu',
  //       },
  //       component: _import('km/procReport/FiveYearPlan'),
  //     },
  //   ]
  // },
  // 程序统计报表
  // {
  //   path: '/ProcReportView',
  //   name: 'ProcReportView',
  //   redirect: { name: 'ProcReportView' },
  //   meta: {
  //     ...meta,
  //     title:'程序统计报表',
  //   },
  //   component: layoutHeaderAside,
  //   children:[
  //     {
  //       path: '',
  //       name: 'ProcReportView',
  //       meta: {
  //         ...meta,
  //         title:'程序统计报表',
  //         fullScreen: 'hideLeftMenu',
  //       },
  //       component: _import('km/procReport/ProcReportView'),
  //     },
  //   ]
  // },
]
