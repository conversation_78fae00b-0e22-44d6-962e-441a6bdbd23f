import layoutHeaderAside from '@/layout/header-aside'

// 由于懒加载页面太多的话会造成webpack热更新太慢，所以开发环境不使用懒加载，只有生产环境使用懒加载
const _import = require('@/libs/util.import.' + process.env.NODE_ENV)

/**
 * 项目管理模块路由配置
 * 
 * @description 项目管理相关页面的路由配置
 * <AUTHOR>
 * @date 2024-01-01
 */
export default [
  {
    path: '/projectMgt',
    name: 'projectMgt',
    component: layoutHeaderAside,
    meta: {
      title: '项目管理',
      cache: false,
      auth: true
    },
    children: [
      {
        path: 'list',
        name: 'projectList',
        component: _import('projectMgt/ProjectList'),
        meta: {
          title: '项目列表',
          cache: false,
          auth: true
        }
      },
      {
        path: 'detail/:id',
        name: 'ProjectDetail',
        component: _import('projectMgt/ProjectDetail'),
        meta: {
          title: '项目详情',
          cache: false,
          auth: true
        }
      },
      {
        path: 'tasks/:projectId',
        name: 'ProjectTasks',
        component: _import('projectMgt/ProjectTasks'),
        meta: {
          title: '任务管理',
          cache: false,
          auth: true
        }
      },
      {
        path: 'members/:projectId',
        name: 'ProjectMembers',
        component: _import('projectMgt/ProjectMembers'),
        meta: {
          title: '成员管理',
          cache: false,
          auth: true
        }
      },
      {
        path: 'statistics',
        name: 'ProjectStatistics',
        component: _import('projectMgt/ProjectStatistics'),
        meta: {
          title: '项目统计',
          cache: false,
          auth: true
        }
      }
    ]
  }
] 