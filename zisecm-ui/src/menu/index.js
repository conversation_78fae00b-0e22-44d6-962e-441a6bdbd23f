import {uniqueId} from 'lodash'
import knowledgeManagement from "./modules/knowledgeManagement";
import projectManagement from "../router/modules/projectMgt";

/**
 * @description 给菜单数据补充上 path 字段
 * @description https://github.com/d2-projects/d2-admin/issues/209
 * @param {Array} menu 原始的菜单数据
 */
function supplementPath (menu) {
  return menu.map(e => ({
    ...e,
    path: e.path || uniqueId('d2-menu-empty-'),
    ...e.children ? {
      children: supplementPath(e.children)
    } : {}
  }))
}

export const menuHeader = supplementPath([
  { path: '/index', title: '首页', icon: 'home' },
  {
    title: '页面',
    icon: 'folder-o',
    path: '/page/page1',
    children: [
      { path: '/page/page1', title: '页面 1' },
      { path: '/page/page2', title: '页面 2' },
      { path: '/page/page3', title: '页面 3' }
    ]

  } ,
  {
    title: '任务中心',
    icon: 'folder-o',
    children: [
      { path: '/todoTask', title: '待办任务' },
      { path: '/doneTask', title: '已办任务' }
    ]
  },
  {
    path: '/typeSearch',
    title: '分类搜索',
    icon: 'file',
  },
  {
    path: '/contentSearch',
    title: '全文搜索',
    icon: 'file',
  },
  {
    title: '知识管理',
    icon: 'puzzle-piece',
    children: [
      ...knowledgeManagement
    ]
  }
])

export const menuAside = supplementPath([
  {
    title: '页面',
    icon: 'folder-o',
    path: '/page',
    children: [
      { path: '/page/page1', title: '页面 1' },
      { path: '/page/page2', title: '页面 2' },
      { path: '/page/page3', title: '页面 3' },
      { path: '/folderViewer', title: '文件夹查看' },
      { path: '/page/page5', title: '富文本' }
    ]
  } ,
  {
    title: '任务中心',
    icon: 'folder-o',
    children: [
      { path: '/todoTask', title: '待办任务' },
      { path: '/doneTask', title: '已办任务' }
    ]
  },
  {
    path: '/maintenance',
    title: '设备维修数据',
    icon: 'file',
  },
  {
    path: '/typeSearch',
    title: '分类查询',
    icon: 'file',
  },
  ...knowledgeManagement
])
