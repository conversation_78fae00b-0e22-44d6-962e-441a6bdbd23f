<style >
.el-dialog{
  border-radius: 7px !important;
}
.el-dialog__body {
  border-top: 1px solid #e8eaec;
}
</style>
<template>
  <div class="d2-layout-header-aside-group" :style="styleLayoutMainGroup" :class="{grayMode: grayActive}">
    <!-- 半透明遮罩 -->
    <div class="d2-layout-header-aside-mask"></div>
    <!-- 主体内容 -->
    <div class="d2-layout-header-aside-content" flex="dir:top">
      <!-- 顶栏 -->
      <div class="d2-theme-header" v-if="!planeActive" :style="{ opacity: this.searchActive ? 0.5 : 1 }" flex-box="0" flex>
          <img v-if="asideCollapse" :src="`${$baseUrl}image/theme/${themeActiveSetting.name}/logo/icon-only.png`" sizes="">
          <img v-else :src="`${$baseUrl}image/theme/${themeActiveSetting.name}/logo/all.png`">
        <div class="toggle-aside-btn" @click="handleToggleAside" flex-box="0">
          <d2-icon name="bars"/>
        </div>
        <d2-menu-header flex-box="1"/>
        <!-- 顶栏右侧 -->
        <div class="d2-header-right" flex-box="0">
          <!-- 如果你只想在开发环境显示这个按钮请添加 v-if="$env === 'development'" -->
<!--          <d2-header-search @click="handleSearchClick"/>-->
<!--          <d2-header-log/>-->
          <div style="text-align: center; line-height: 1.5;">
            <div style="color: red; font-weight: bold;">上网不涉密，涉密不上网</div>
            <div style="color: red; font-weight: bold;">系统禁止处理、传递国家及商业机密</div>
          </div>
          <d2-header-fullscreen/>
<!--          <d2-header-theme/>-->
<!--          <d2-header-locales/>-->
          <!-- 调整界面的大小与颜色功能删除 -->
<!--          <d2-header-size/>-->
<!--          <d2-header-color/>-->
              <feedback></feedback>
              <forward-old-system/>
          <d2-header-user/>
        </div>
      </div>
      <!-- 下面 主体 -->
      <div class="d2-theme-container" flex-box="1" flex>
        <!-- 主体 侧边栏 -->
        <!-- 此方法暂时忽略，使用此方法将改变布局，在routes中配置fullscreen: true,即可实现   v-if="$store.state.isShowSide"-->
        <div
          v-if="!planeActive"
          flex-box="0"
          ref="aside"
          :class="{'d2-theme-container-aside': true, 'd2-theme-container-transition': asideTransition}"
          :style="{
            width: widthChange,
            opacity: this.searchActive ? 0.5 : 1,
            maxHeight: 'calc(100vh - 50px)',
            overflowY: 'auto'
          }">
          <d2-menu-side v-if="showMenuContentHandle"/>
        </div>
        <!-- 主体 -->
        <div class="d2-theme-container-main" flex-box="1" flex>
          <!-- 搜索 -->
          <transition name="fade-scale" key="tran001">
            <div v-if="searchActive" class="d2-theme-container-main-layer" flex>
              <d2-panel-search ref="panelSearch" @close="searchPanelClose"/>
            </div>
          </transition>
          <!-- 内容 -->
          <transition name="fade-scale" key="tran002">
            <div v-if="!searchActive" class="d2-theme-container-main-layer" flex="dir:top">
              <!-- tab -->
              <div class="d2-theme-container-main-header" flex-box="0">
                <d2-tabs/>
              </div>
              <!-- 页面 -->
              <div class="d2-theme-container-main-body" flex-box="1" style="margin-bottom: 10px;">
                <transition :name="transitionActive ? 'fade-transverse' : ''" key="tran003">
                  <keep-alive :include="keepAlive">
                    <router-view :key="routerViewKey" />
                  </keep-alive>
                </transition>
              </div>
            </div>
          </transition>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import d2MenuSide from './components/menu-side'
  import d2MenuHeader from './components/menu-header'
  import d2Tabs from './components/tabs'
  import d2HeaderFullscreen from './components/header-fullscreen'
  import d2HeaderLocales from './components/header-locales'
  import d2HeaderSearch from './components/header-search'
  import d2HeaderSize from './components/header-size'
  import d2HeaderTheme from './components/header-theme'
  import d2HeaderUser from './components/header-user'
  import d2HeaderLog from './components/header-log'
  import d2HeaderColor from './components/header-color'
  import forwardOldSystem from "./components/forwardOldSystem/lastSystem";
  import {mapActions, mapGetters, mapState} from 'vuex'
  import mixinSearch from './mixins/search'
  import Feedback from "@/layout/header-aside/components/problemFeedback/feedback";
  export default {
  name: 'd2-layout-header-aside',
  mixins: [
    mixinSearch
  ],
  components: {
    Feedback,
    d2MenuSide,
    d2MenuHeader,
    d2Tabs,
    d2HeaderFullscreen,
    d2HeaderLocales,
    d2HeaderSearch,
    d2HeaderSize,
    d2HeaderTheme,
    d2HeaderUser,
    d2HeaderLog,
    d2HeaderColor,
    forwardOldSystem
  },
  data () {
    return {
      // [侧边栏宽度] 正常状态
      asideWidth: '200px',
      // [侧边栏宽度] 折叠状态
      asideWidthCollapse: '65px'
    }
  },
  computed: {
    ...mapState('d2admin', {
      keepAlive: state => state.page.keepAlive,
      grayActive: state => state.gray.active,
      transitionActive: state => state.transition.active,
      asideCollapse: state => state.menu.asideCollapse,
      asideTransition: state => state.menu.asideTransition
    }),
    ...mapState('d2admin/fullscreen', [
      'planeActive',
    ]),
    ...mapGetters('d2admin', {
      themeActiveSetting: 'theme/activeSetting'
    }),
    /**
     * @description 用来实现带参路由的缓存
     */
    routerViewKey () {
      // 默认情况下 key 类似 __transition-n-/foo
      // 这里的字符串操作是为了最终 key 的格式和原来相同 类似 __transition-n-__stamp-time-/foo
      const stamp = this.$route.meta[`__stamp-${this.$route.path}`] || ''
      return `${stamp ? `__stamp-${stamp}-` : ''}${this.$route.path}`
    },
    /**
     * @description 最外层容器的背景图片样式
     */
    styleLayoutMainGroup () {
      return this.themeActiveSetting.backgroundImage
        ? { backgroundImage: `url('${this.$baseUrl}${this.themeActiveSetting.backgroundImage}')` }
        : {}
    },
    widthChange(){
      //如果非全屏
      if(this.$route.meta?.fullScreen === 'hideLeftMenu'){
        return "10px";
      }
      else{
        return this.asideCollapse ? this.asideWidthCollapse : this.asideWidth;
      }
    },
    showMenuContentHandle(){
      return !(this.$route.meta.fullScreen === 'hideLeftMenu')
    }
  },
  methods: {
    ...mapActions('d2admin/menu', [
      'asideCollapseToggle'
    ]),
    /**
     * 接收点击切换侧边栏的按钮
     */
    handleToggleAside () {
      this.asideCollapseToggle()
    }
  }
}
</script>

<style lang="scss">
// 注册主题
@import '~@/assets/style/theme/register.scss';
</style>
